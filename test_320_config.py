#!/usr/bin/env python3
"""
测试320×320配置的数据加载
"""

import torch
import numpy as np
from pathlib import Path
import yaml

# 导入数据集
from daogu_dataset import DaoGuSRFDataset

def test_320_config():
    """测试320×320配置"""
    print("🔍 测试320×320配置的数据加载")
    print("=" * 50)
    
    # 加载配置
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print(f"配置中的crop_size: {config['train']['dataset']['crop_size']}")
    
    # 创建数据集
    try:
        dataset = DaoGuSRFDataset(
            root='data/daogu_processed',
            input='img1',
            ref='img2', 
            names_path='train.txt',
            sf=4,
            crop_size=[320, 320],
            repeat=1,
            use_cache=False
        )
        
        print(f"✅ 数据集创建成功，样本数量: {len(dataset)}")
        
        # 测试加载第一个样本
        print("\n📊 测试加载第一个样本...")
        sample_data = dataset[0]
        
        # 解包数据
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample_data
        
        print(f"✅ 数据加载成功！")
        print(f"  HSI HR: {hsi_hr.shape}")
        print(f"  HSI LR: {hsi_lr.shape}")
        print(f"  HSI RGB HR: {hsi_rgb_hr.shape}")
        print(f"  HSI RGB LR: {hsi_rgb_lr.shape}")
        print(f"  RGB HR: {rgb_hr.shape}")
        print(f"  RGB LR: {rgb_lr.shape}")
        
        # 检查数据类型和范围
        print(f"\n📈 数据统计:")
        print(f"  HSI HR - 类型: {hsi_hr.dtype}, 范围: [{hsi_hr.min():.3f}, {hsi_hr.max():.3f}]")
        print(f"  RGB HR - 类型: {rgb_hr.dtype}, 范围: [{rgb_hr.min():.3f}, {rgb_hr.max():.3f}]")
        
        # 测试模型兼容性
        print(f"\n🔧 测试模型兼容性...")
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 转换为tensor
        hsi_hr_tensor = torch.from_numpy(hsi_hr).unsqueeze(0).to(device)
        hsi_lr_tensor = torch.from_numpy(hsi_lr).unsqueeze(0).to(device)
        hsi_rgb_lr_tensor = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).to(device)
        rgb_lr_tensor = torch.from_numpy(rgb_lr).unsqueeze(0).to(device)
        
        print(f"  Tensor形状:")
        print(f"    HSI HR: {hsi_hr_tensor.shape}")
        print(f"    HSI LR: {hsi_lr_tensor.shape}")
        print(f"    HSI RGB LR: {hsi_rgb_lr_tensor.shape}")
        print(f"    RGB LR: {rgb_lr_tensor.shape}")
        
        # 测试多个样本
        print(f"\n📋 测试多个样本...")
        for i in range(min(3, len(dataset))):
            sample = dataset[i]
            name = dataset.names[i]
            hsi_hr_shape = sample[0].shape
            print(f"  样本 {i+1} ({name}): HSI HR {hsi_hr_shape}")
        
        print(f"\n✅ 320×320配置测试完成！数据加载正常，可以开始训练。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_320_config()
