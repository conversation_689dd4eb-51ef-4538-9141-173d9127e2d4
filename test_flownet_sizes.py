#!/usr/bin/env python3
"""
测试FlowNet对不同尺寸的支持
"""

import torch
import numpy as np
from hsirsr.model.refsr import CrossNetHSI

def test_size(h, w):
    """测试特定尺寸"""
    print(f"\n测试尺寸: {h}x{w}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 创建模型
        model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
        model.eval()
        
        # 创建测试数据
        hsi_lr = torch.randn(1, 1, 273, h, w).to(device)
        hsi_rgb_lr = torch.randn(1, 3, h, w).to(device)
        rgb_lr = torch.randn(1, 3, h, w).to(device)
        
        print(f"  输入形状: HSI {hsi_lr.shape}, HSI_RGB {hsi_rgb_lr.shape}, RGB {rgb_lr.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = model(hsi_lr, hsi_rgb_lr, rgb_lr)
        
        print(f"  ✅ 成功! 输出形状: {output.shape}")
        return True
        
    except Exception as e:
        print(f"  ❌ 失败: {e}")
        return False

def main():
    print("🔍 测试FlowNet支持的尺寸")
    print("=" * 50)
    
    # 测试常见的32倍数尺寸
    test_sizes = [
        (256, 256),   # 标准正方形
        (320, 320),   # 较大正方形
        (256, 512),   # 矩形
        (320, 512),   # 更大矩形
        (384, 384),   # 我们的数据尺寸
        (416, 384),   # 当前问题尺寸
        (448, 384),   # 稍大一点
        (512, 384),   # 更大
        (480, 384),   # 接近我们的最大尺寸
    ]
    
    successful_sizes = []
    
    for h, w in test_sizes:
        if test_size(h, w):
            successful_sizes.append((h, w))
    
    print(f"\n📊 测试结果:")
    print(f"成功的尺寸: {successful_sizes}")
    
    if successful_sizes:
        print(f"\n✅ 推荐使用的尺寸:")
        for h, w in successful_sizes:
            print(f"  {h}x{w}")
    else:
        print(f"\n❌ 没有找到可用的尺寸")

if __name__ == '__main__':
    main()
