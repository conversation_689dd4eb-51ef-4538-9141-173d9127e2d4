#!/usr/bin/env python3
"""
DaoGu数据集HSI-RefSR模型训练脚本
"""

import os
import sys
import argparse
from pathlib import Path
import yaml
import torch
import torch.nn as nn
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入必要的模块
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI
from hsirsr.module.refsr import CommonModule
from torchlight.trainer.engine import Engine
from torchlight.utils.reproducibility import setup_randomness
from torchlight.utils.helper import get_obj

def create_data_loaders(config):
    """创建训练和测试数据加载器"""
    print("=== 创建数据加载器 ===")
    
    # 训练数据集
    train_dataset = DaoGuSRFDataset(
        root=config['train']['dataset']['root'],
        input=config['train']['dataset']['input'],
        ref=config['train']['dataset']['ref'],
        names_path=config['train']['dataset']['names_path'],
        sf=config['train']['dataset']['sf'],
        crop_size=config['train']['dataset']['crop_size'],
        repeat=config['train']['dataset']['repeat'],
        use_cache=config['train']['dataset']['use_cache']
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['train']['loader']['batch_size'],
        shuffle=config['train']['loader']['shuffle'],
        num_workers=config['train']['loader']['num_workers'],
        pin_memory=config['train']['loader']['pin_memory']
    )
    
    # 测试数据集
    test_dataset = DaoGuSRFDataset(
        root=config['test']['dataset']['root'],
        input=config['test']['dataset']['input'],
        ref=config['test']['dataset']['ref'],
        names_path=config['test']['dataset']['names_path'],
        sf=config['test']['dataset']['sf'],
        use_cache=config['test']['dataset']['use_cache']
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['test']['loader']['batch_size'],
        shuffle=config['test']['loader']['shuffle']
    )
    
    print(f"训练样本数量: {len(train_dataset)}")
    print(f"测试样本数量: {len(test_dataset)}")
    print(f"训练批次数量: {len(train_loader)}")
    print(f"测试批次数量: {len(test_loader)}")
    
    return train_loader, test_loader

def create_model_and_optimizer(config):
    """创建模型和优化器"""
    print("=== 创建模型和优化器 ===")
    
    # 创建模型
    model = CrossNetHSI(
        use_mask=config['module']['model']['use_mask'],
        use_pwc=config['module']['model']['use_pwc'],
        reweight=config['module']['model']['reweight']
    )
    
    # 计算模型参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    print(f"模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config['module']['optimizer']['lr'],
        weight_decay=config['module']['optimizer']['weight_decay']
    )
    
    # 创建训练模块
    module = CommonModule(
        model=config['module']['model'],
        optimizer=config['module']['optimizer']
    )

    # 手动设置模型和优化器
    module.model = model
    module.optimizer = optimizer
    
    return module

def train_model(config, save_dir):
    """训练模型"""
    print("=== 开始训练 ===")
    
    # 设置随机种子
    setup_randomness(2021, deterministic=False)
    
    # 创建数据加载器
    train_loader, test_loader = create_data_loaders(config)
    
    # 创建模型和优化器
    module = create_model_and_optimizer(config)
    
    # 创建训练引擎
    engine = Engine(module, save_dir=save_dir, **config['engine'])
    
    # 保存配置文件
    config_save_path = os.path.join(save_dir, 'config.yaml')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    print(f"配置文件已保存到: {config_save_path}")
    
    # 开始训练
    try:
        engine.train(train_loader, valid_loader=test_loader)
        print("✅ 训练完成！")
    except KeyboardInterrupt:
        print("⚠️ 训练被用户中断")
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DaoGu数据集HSI-RefSR模型训练')
    parser.add_argument('--config', '-c', default='daogu_config.yaml', 
                       help='配置文件路径 (默认: daogu_config.yaml)')
    parser.add_argument('--save_dir', '-s', default='saved/daogu-training', 
                       help='模型保存目录 (默认: saved/daogu-training)')
    parser.add_argument('--resume', '-r', default=None, 
                       help='恢复训练的检查点路径')
    
    args = parser.parse_args()
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    print("=" * 80)
    print("🚀 DaoGu数据集HSI-RefSR模型训练")
    print("=" * 80)
    print(f"配置文件: {args.config}")
    print(f"保存目录: {args.save_dir}")
    print(f"最大训练轮数: {config['engine']['max_epochs']}")
    print(f"学习率: {config['module']['optimizer']['lr']}")
    print(f"批大小: {config['train']['loader']['batch_size']}")
    
    # 检查GPU
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("⚠️ 未检测到GPU，将使用CPU训练（速度较慢）")
    
    # 开始训练
    train_model(config, args.save_dir)

if __name__ == '__main__':
    main()
