#!/usr/bin/env python3
"""
查看测试结果图片
"""

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from pathlib import Path

def view_results():
    """查看所有测试结果图片"""
    results_dir = Path("test_results")
    
    # 图片文件列表
    image_files = [
        ("comprehensive_test_results.png", "综合测试结果"),
        ("sample_processing_results.png", "样本处理结果"),
        ("model_architecture_summary.png", "模型架构总结")
    ]
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(24, 8))
    fig.suptitle('DaoGu数据集HSI-RefSR模型测试结果总览', fontsize=16, fontweight='bold')
    
    for i, (filename, title) in enumerate(image_files):
        filepath = results_dir / filename
        if filepath.exists():
            img = mpimg.imread(str(filepath))
            axes[i].imshow(img)
            axes[i].set_title(title, fontsize=14)
            axes[i].axis('off')
        else:
            axes[i].text(0.5, 0.5, f'文件不存在:\n{filename}', 
                        ha='center', va='center', transform=axes[i].transAxes)
            axes[i].set_title(title, fontsize=14)
            axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 保存总览图
    overview_path = results_dir / "results_overview.png"
    fig.savefig(overview_path, dpi=300, bbox_inches='tight')
    print(f"结果总览图已保存到: {overview_path}")

if __name__ == "__main__":
    view_results()
