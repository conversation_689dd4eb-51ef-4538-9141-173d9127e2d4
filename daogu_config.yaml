train:
    dataset:
        type@: daogu_dataset.DaoGuSRFDataset
        input: img1
        ref: img2
        names_path: train.txt
        sf: 4
        crop_size: [64, 64]
        repeat: 1
        use_cache: true
        root: data/daogu_processed
    loader:
        batch_size: 1
        shuffle: true
        num_workers: 1
        pin_memory: true
test:
    dataset:
        type@: daogu_dataset.DaoGuSRFDataset
        input: img1
        ref: img2
        names_path: test.txt
        sf: 4
        crop_size: [64, 64]
        repeat: 1
        use_cache: true
        root: data/daogu_processed
    loader:
        batch_size: 1
        shuffle: false
engine:
    max_epochs: 100
    mnt_metric: val_psnr
    mnt_mode: max
    log_img_step: 5
    valid_log_img_step: 1
    pbar: qqdm
    save_per_epoch: 5
    enable_tensorboard: false
    log_step: 10
    valid_per_epoch: 1
    enable_early_stop: true
    early_stop_threshold: 0.01
    early_stop_count: 10
    num_fmt: '{:8.5g}'
    ckpt_save_mode: best
module:
    type@: refsr.CommonModule
    optimizer:
        type@: AdamW
        lr: 5.0e-05
        weight_decay: 1.0e-04
    model:
        type@: CrossNetHSI
        use_mask: true
        use_pwc: false
        reweight: false
