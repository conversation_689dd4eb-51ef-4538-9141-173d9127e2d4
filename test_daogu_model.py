"""
测试DaoGu数据集与HSI-RefSR模型的兼容性
"""
import yaml
import torch
from daogu_dataset import DaoGuSRFDataset
from torch.utils.data import DataLoader

def test_dataset_compatibility():
    """测试数据集兼容性"""
    print("=== 测试DaoGu数据集兼容性 ===")
    
    # 加载配置
    with open('daogu_config.yaml', 'r') as f:
        cfg = yaml.safe_load(f)
    
    # 创建数据集
    train_cfg = cfg['train']['dataset'].copy()
    train_cfg.pop('type@', None)
    train_dataset = DaoGuSRFDataset(**train_cfg)
    
    test_cfg = cfg['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    
    print(f"训练数据集: {len(train_dataset)} 个样本")
    print(f"测试数据集: {len(test_dataset)} 个样本")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=1, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 测试数据加载
    print("\n=== 测试数据加载 ===")
    data_names = ['hsi_hr', 'hsi_lr', 'hsi_rgb_hr', 'hsi_rgb_lr', 'rgb_hr', 'rgb_lr']
    for i, batch in enumerate(train_loader):
        print(f"训练样本 {i+1}:")
        for j, data in enumerate(batch):
            if hasattr(data, 'shape'):
                print(f"  {data_names[j]}: {data.shape} ({data.dtype})")

        if i >= 2:  # 只测试前3个样本
            break

    print("\n=== 测试数据范围 ===")
    sample = next(iter(train_loader))
    for j, data in enumerate(sample):
        if hasattr(data, 'shape') and len(data.shape) > 1:
            print(f"{data_names[j]}: min={data.min():.4f}, max={data.max():.4f}, mean={data.mean():.4f}")
    
    print("\n=== 数据集测试完成 ===")
    return True

def test_model_with_real_data():
    """使用真实数据测试模型"""
    print("\n=== 使用真实数据测试模型 ===")

    try:
        # 加载配置和数据集
        with open('daogu_config.yaml', 'r') as f:
            cfg = yaml.safe_load(f)

        train_cfg = cfg['train']['dataset'].copy()
        train_cfg.pop('type@', None)
        train_dataset = DaoGuSRFDataset(**train_cfg)

        # 获取一个样本
        sample = train_dataset[0]
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample

        print(f"真实数据形状:")
        print(f"  hsi_lr: {hsi_lr.shape}")
        print(f"  hsi_rgb_lr: {hsi_rgb_lr.shape}")
        print(f"  rgb_hr: {rgb_hr.shape}")

        # 添加batch维度
        hsi_lr = torch.from_numpy(hsi_lr).unsqueeze(0).float()
        hsi_rgb_lr = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).float()
        rgb_hr = torch.from_numpy(rgb_hr).unsqueeze(0).float()

        print(f"添加batch维度后:")
        print(f"  hsi_lr: {hsi_lr.shape}")
        print(f"  hsi_rgb_lr: {hsi_rgb_lr.shape}")
        print(f"  rgb_hr: {rgb_hr.shape}")

        # 由于rgb_hr和hsi_rgb_lr尺寸不匹配，我们需要调整
        # 将rgb_hr上采样到与hsi_rgb_lr相同的尺寸
        import torch.nn.functional as F
        target_h, target_w = hsi_rgb_lr.shape[2], hsi_rgb_lr.shape[3]
        rgb_hr_resized = F.interpolate(rgb_hr, size=(target_h, target_w), mode='bilinear', align_corners=False)

        print(f"调整尺寸后:")
        print(f"  rgb_hr_resized: {rgb_hr_resized.shape}")

        # 导入并创建模型
        from hsirsr.model.refsr import CrossNetHSI
        model = CrossNetHSI(
            reweight=False,
            use_mask=True,
            use_pwc=False  # 避免下载预训练权重
        )

        print("模型创建成功")
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

        # 测试前向传播
        model.eval()
        with torch.no_grad():
            output, ref_warp, flow, masks = model(hsi_lr, hsi_rgb_lr, rgb_hr_resized)
            print(f"输出形状: {output.shape}")
            print(f"ref_warp形状: {ref_warp.shape}")
            print(f"flow形状: {flow.shape}")
            print(f"masks数量: {len(masks)}")

        print("✅ 真实数据模型测试成功！")
        return True

    except Exception as e:
        print(f"模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 测试数据集
    dataset_ok = test_dataset_compatibility()
    
    # 测试模型
    model_ok = test_model_with_real_data()
    
    if dataset_ok and model_ok:
        print("\n✅ 所有测试通过！DaoGu数据集与HSI-RefSR模型兼容")
    else:
        print("\n❌ 测试失败，需要进一步调试")
