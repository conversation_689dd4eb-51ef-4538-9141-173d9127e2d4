"""
测试DaoGu数据集与HSI-RefSR模型的兼容性
"""
import yaml
import torch
from daogu_dataset import DaoGuSRFDataset
from torch.utils.data import DataLoader

def test_dataset_compatibility():
    """测试数据集兼容性"""
    print("=== 测试DaoGu数据集兼容性 ===")
    
    # 加载配置
    with open('daogu_config.yaml', 'r') as f:
        cfg = yaml.safe_load(f)
    
    # 创建数据集
    train_cfg = cfg['train']['dataset'].copy()
    train_cfg.pop('type@', None)
    train_dataset = DaoGuSRFDataset(**train_cfg)
    
    test_cfg = cfg['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    
    print(f"训练数据集: {len(train_dataset)} 个样本")
    print(f"测试数据集: {len(test_dataset)} 个样本")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=1, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 测试数据加载
    print("\n=== 测试数据加载 ===")
    for i, batch in enumerate(train_loader):
        print(f"训练样本 {i+1}:")
        for key, value in batch.items():
            if hasattr(value, 'shape'):
                print(f"  {key}: {value.shape} ({value.dtype})")
            else:
                print(f"  {key}: {value}")
        
        if i >= 2:  # 只测试前3个样本
            break
    
    print("\n=== 测试数据范围 ===")
    sample = next(iter(train_loader))
    for key, value in sample.items():
        if hasattr(value, 'shape') and len(value.shape) > 1:
            print(f"{key}: min={value.min():.4f}, max={value.max():.4f}, mean={value.mean():.4f}")
    
    print("\n=== 数据集测试完成 ===")
    return True

def test_model_forward():
    """测试模型前向传播"""
    print("\n=== 测试模型前向传播 ===")
    
    try:
        # 尝试导入模型
        from hsirsr.model.refsr import CrossNetHSI
        print("模型导入成功")

        # 创建简单的测试数据
        batch_size = 1
        hsi_channels = 273
        rgb_channels = 3
        height, width = 64, 64

        # 模拟数据 - 确保尺寸匹配
        hsi_lr = torch.randn(batch_size, hsi_channels, height//4, width//4)
        hsi_rgb_lr = torch.randn(batch_size, rgb_channels, height//4, width//4)
        rgb_hr = torch.randn(batch_size, rgb_channels, height//4, width//4)  # 与hsi_rgb_lr相同尺寸

        print(f"输入数据形状:")
        print(f"  hsi_lr: {hsi_lr.shape}")
        print(f"  hsi_rgb_lr: {hsi_rgb_lr.shape}")
        print(f"  rgb_hr: {rgb_hr.shape}")

        # 创建模型（不下载预训练权重）
        model = CrossNetHSI(
            reweight=False,
            use_mask=True,
            use_pwc=False  # 避免下载预训练权重
        )
        
        print("模型创建成功")
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试前向传播
        model.eval()
        with torch.no_grad():
            output = model(hsi_lr, hsi_rgb_lr, rgb_hr)
            print(f"输出形状: {output.shape}")
        
        print("模型前向传播测试成功")
        return True
        
    except Exception as e:
        print(f"模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 测试数据集
    dataset_ok = test_dataset_compatibility()
    
    # 测试模型
    model_ok = test_model_forward()
    
    if dataset_ok and model_ok:
        print("\n✅ 所有测试通过！DaoGu数据集与HSI-RefSR模型兼容")
    else:
        print("\n❌ 测试失败，需要进一步调试")
