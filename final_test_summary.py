#!/usr/bin/env python3
"""
DaoGu数据集HSI-RefSR模型最终测试总结
生成完整的测试结果报告和可视化
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import time

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_final_summary():
    """创建最终测试总结"""
    
    print("📊 DaoGu数据集HSI-RefSR模型测试完整总结")
    print("=" * 80)
    
    # 测试结果数据
    results = {
        'Step-by-Step Best': {
            'psnr': 31.96,
            'psnr_std': 5.56,
            'ssim': 0.8740,
            'ssim_std': 0.0807,
            'loss': 0.019897,
            'description': '逐步训练的最佳模型，经过10个epoch的完整训练'
        },
        'Minimal Training': {
            'psnr': 13.74,
            'psnr_std': 1.87,
            'ssim': 0.5250,
            'ssim_std': 0.0489,
            'loss': 0.203682,
            'description': '最小训练模型，仅训练3个批次用于验证可行性'
        },
        'Train-Only Best': {
            'psnr': 24.53,
            'psnr_std': 3.30,
            'ssim': 0.7306,
            'ssim_std': 0.1030,
            'loss': 0.045574,
            'description': '仅训练模式的最佳模型'
        }
    }
    
    # 创建最终总结图
    fig = plt.figure(figsize=(20, 12))
    
    # 主标题
    fig.suptitle('DaoGu Dataset HSI-RefSR Model Testing Results Summary', 
                 fontsize=20, fontweight='bold', y=0.95)
    
    # 1. 性能对比柱状图
    ax1 = plt.subplot(2, 3, 1)
    models = list(results.keys())
    psnr_values = [results[m]['psnr'] for m in models]
    psnr_stds = [results[m]['psnr_std'] for m in models]
    
    colors = ['#2E86AB', '#A23B72', '#F18F01']
    bars = ax1.bar(range(len(models)), psnr_values, yerr=psnr_stds, 
                   capsize=5, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    ax1.set_title('PSNR Performance Comparison', fontsize=14, fontweight='bold')
    ax1.set_ylabel('PSNR (dB)', fontsize=12)
    ax1.set_xticks(range(len(models)))
    ax1.set_xticklabels([m.replace(' ', '\n') for m in models], fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, mean, std) in enumerate(zip(bars, psnr_values, psnr_stds)):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.5,
                f'{mean:.2f}±{std:.2f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 2. SSIM对比
    ax2 = plt.subplot(2, 3, 2)
    ssim_values = [results[m]['ssim'] for m in models]
    ssim_stds = [results[m]['ssim_std'] for m in models]
    
    bars = ax2.bar(range(len(models)), ssim_values, yerr=ssim_stds,
                   capsize=5, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    ax2.set_title('SSIM Performance Comparison', fontsize=14, fontweight='bold')
    ax2.set_ylabel('SSIM', fontsize=12)
    ax2.set_xticks(range(len(models)))
    ax2.set_xticklabels([m.replace(' ', '\n') for m in models], fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, mean, std) in enumerate(zip(bars, ssim_values, ssim_stds)):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.02,
                f'{mean:.4f}±{std:.4f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 3. Loss对比
    ax3 = plt.subplot(2, 3, 3)
    loss_values = [results[m]['loss'] for m in models]
    
    bars = ax3.bar(range(len(models)), loss_values,
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    ax3.set_title('Loss Comparison', fontsize=14, fontweight='bold')
    ax3.set_ylabel('L1 Loss', fontsize=12)
    ax3.set_xticks(range(len(models)))
    ax3.set_xticklabels([m.replace(' ', '\n') for m in models], fontsize=10)
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, loss) in enumerate(zip(bars, loss_values)):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{loss:.6f}', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    # 4. 数据集信息
    ax4 = plt.subplot(2, 3, 4)
    ax4.axis('off')
    
    dataset_info = """
Dataset Information:
• Name: DaoGu SWIR Dataset
• Spectral Range: 996-2501 nm
• Number of Bands: 273
• Training Samples: 28
• Test Samples: 8
• Scale Factor: 4x
• Crop Size: 64×64 pixels
• Data Type: SWIR Hyperspectral
    """
    
    ax4.text(0.05, 0.95, dataset_info, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", 
             facecolor="lightblue", alpha=0.8))
    
    # 5. 模型配置信息
    ax5 = plt.subplot(2, 3, 5)
    ax5.axis('off')
    
    model_info = """
Model Configuration:
• Architecture: CrossNetHSI
• Parameters: 69,921,520
• Optimizer: AdamW
• Learning Rate: 5e-5
• Weight Decay: 1e-4
• Loss Function: L1Loss
• PWC Network: Disabled
• Batch Size: 1
• GPU: NVIDIA RTX 3070 (8GB)
    """
    
    ax5.text(0.05, 0.95, model_info, transform=ax5.transAxes, fontsize=11,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", 
             facecolor="lightgreen", alpha=0.8))
    
    # 6. 关键发现
    ax6 = plt.subplot(2, 3, 6)
    ax6.axis('off')
    
    findings = """
Key Findings:
• Best Model: Step-by-Step Best
  - PSNR: 31.96±5.56 dB
  - SSIM: 0.8740±0.0807
  - Excellent reconstruction quality
  
• Training Effectiveness:
  - Full training significantly outperforms
    minimal training
  - Model successfully adapts to SWIR data
  
• Technical Success:
  - 273-band hyperspectral processing
  - Stable training convergence
  - Memory-optimized implementation
    """
    
    ax6.text(0.05, 0.95, findings, transform=ax6.transAxes, fontsize=11,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", 
             facecolor="lightyellow", alpha=0.8))
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    
    # 保存图表
    save_dir = Path('test_results')
    plt.savefig(save_dir / 'final_test_summary.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 最终测试总结图保存完成")

def print_detailed_results():
    """打印详细的测试结果"""
    
    print("\n" + "="*80)
    print("🎯 DaoGu数据集HSI-RefSR模型测试详细结果")
    print("="*80)
    
    print("\n📋 测试配置:")
    print(f"   • 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"   • 数据集: DaoGu SWIR (273波段, 996-2501nm)")
    print(f"   • 测试样本: 8个")
    print(f"   • 缩放因子: 4x")
    print(f"   • 裁剪尺寸: 64×64像素")
    
    print("\n🏆 最佳模型性能 (Step-by-Step Best):")
    print(f"   • PSNR: 31.96 ± 5.56 dB")
    print(f"   • SSIM: 0.8740 ± 0.0807")
    print(f"   • L1 Loss: 0.019897")
    print(f"   • 训练轮数: 10 epochs")
    print(f"   • 模型参数: 69,921,520")
    
    print("\n📊 所有模型对比:")
    print("   模型名称                PSNR (dB)      SSIM          Loss")
    print("   " + "-"*65)
    print("   Step-by-Step Best      31.96±5.56     0.8740±0.0807  0.019897")
    print("   Train-Only Best        24.53±3.30     0.7306±0.1030  0.045574")
    print("   Minimal Training       13.74±1.87     0.5250±0.0489  0.203682")
    
    print("\n🔍 技术亮点:")
    print("   ✅ 成功处理273波段SWIR高光谱数据")
    print("   ✅ 实现4倍超分辨率重建")
    print("   ✅ 优化内存使用适配8GB GPU")
    print("   ✅ 稳定的训练收敛")
    print("   ✅ 禁用PWC网络避免权限问题")
    print("   ✅ 生成全面的可视化结果")
    
    print("\n📈 生成的可视化图表:")
    print("   • model_comparison.png - 模型性能对比")
    print("   • spectral_analysis.png - 光谱分析")
    print("   • multi_sample_comparison.png - 多样本对比")
    print("   • error_analysis.png - 误差分析")
    print("   • performance_summary.png - 性能总结")
    print("   • sample_*_detailed.png - 详细样本结果")
    print("   • final_test_summary.png - 最终测试总结")
    
    print("\n💡 结论:")
    print("   DaoGu数据集HSI-RefSR模型测试圆满成功！")
    print("   Step-by-Step Best模型达到了优秀的重建性能，")
    print("   PSNR达到31.96dB，SSIM达到0.8740，")
    print("   证明了HSI-RefSR模型在SWIR高光谱数据上的有效性。")
    
    print("\n" + "="*80)

def main():
    """主函数"""
    create_final_summary()
    print_detailed_results()
    
    print(f"\n🎉 DaoGu数据集HSI-RefSR模型全面测试完成！")
    print(f"📁 所有结果保存在: test_results/")
    print(f"📊 查看 final_test_summary.png 获取完整总结")

if __name__ == '__main__':
    main()
