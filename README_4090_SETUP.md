# DaoGu数据集HSI-RefSR 4090训练指南

## 🚀 概述

本指南专为RTX 4090用户设计，提供完整的原尺寸图像训练和测试方案。

## 📋 准备工作

### 1. 环境检查
```bash
python check_4090_ready.py
```

### 2. PWC权重下载（可选，推荐）
```bash
python download_pwc_weights.py
```

## 🔧 配置文件

### 基础配置（无PWC）
- 文件：`daogu_config.yaml`
- 特点：不使用PWC对齐，内存需求较低
- 适用：所有GPU

### 4090优化配置（含PWC）
- 文件：`daogu_config_4090.yaml`
- 特点：启用PWC对齐，更好的对齐效果
- 适用：RTX 4090或24GB+显存GPU

## 🏃‍♂️ 训练

### 方法1：使用启动脚本（推荐）
```bash
# 如果运行了download_pwc_weights.py，会生成此脚本
start_4090_training.bat
```

### 方法2：手动启动
```bash
# 基础版本（无PWC）
python train_daogu_4090.py --config daogu_config.yaml

# 4090优化版本（含PWC）
set TORCH_HOME=cache/torch
python train_daogu_4090.py --config daogu_config_4090.yaml
```

## 🧪 测试

### 原尺寸测试
```bash
python test_daogu_4090.py
```

### 测试结果
- 保存位置：`original_size_4090_results/`
- 包含内容：
  - 每个样本的详细分析
  - 对齐前后差异图
  - 超分辨率结果
  - 评估指标（PSNR, SSIM）

## 📊 预期结果

### 数据集信息
- 训练样本：28个
- 测试样本：8个
- 图像尺寸：152×384 到 481×384
- 波段数：273（SWIR）

### 性能指标
- 训练时间：约2-4小时（4090）
- 推理速度：约0.1-0.5秒/图像
- 内存使用：约8-16GB（4090）

## 🔍 故障排除

### 1. PWC权限问题
```bash
# 解决方案1：运行PWC下载脚本
python download_pwc_weights.py

# 解决方案2：使用无PWC配置
python train_daogu_4090.py --config daogu_config.yaml
```

### 2. 内存不足
```bash
# 检查GPU内存
nvidia-smi

# 如果不是4090，使用较小配置
python train_daogu.py  # 使用原始64x64配置
```

### 3. FlowNet尺寸错误
- 原因：某些图像尺寸不满足FlowNet要求
- 解决：数据集已自动调整为32的倍数

## 📁 文件说明

### 训练相关
- `train_daogu_4090.py`：4090优化训练脚本
- `daogu_config.yaml`：基础配置文件
- `daogu_config_4090.yaml`：4090优化配置
- `download_pwc_weights.py`：PWC权重下载脚本

### 测试相关
- `test_daogu_4090.py`：原尺寸测试脚本
- `check_4090_ready.py`：环境检查脚本

### 数据集
- `daogu_dataset.py`：数据加载器（已修复尺寸问题）

## 🎯 关键改进

1. **原尺寸支持**：`crop_size: null`
2. **尺寸对齐**：自动调整为32的倍数
3. **内存优化**：适配4090的24GB显存
4. **PWC支持**：解决权限问题
5. **完整测试**：包含对齐分析

## 📈 预期提升

相比64×64裁剪版本：
- ✅ 真实场景适用性
- ✅ 完整图像上下文
- ✅ 更好的对齐效果
- ✅ 实用的超分辨率结果

## 🚨 注意事项

1. **GPU要求**：推荐RTX 4090或24GB+显存
2. **训练时间**：原尺寸训练时间较长
3. **存储空间**：结果文件较大
4. **PWC权重**：首次运行需要下载

## 📞 支持

如遇问题，请检查：
1. GPU显存是否足够
2. CUDA环境是否正确
3. 数据集路径是否正确
4. 配置文件是否匹配GPU能力
