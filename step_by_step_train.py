#!/usr/bin/env python3
"""
逐步执行训练脚本 - 每一步都有详细输出
"""

import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path

def print_step(step_num, description):
    print(f"\n{'='*60}")
    print(f"Step {step_num}: {description}")
    print(f"{'='*60}")

def main():
    print_step(1, "开始训练准备")
    
    print_step(2, "导入模块")
    from daogu_dataset import DaoGuSRFDataset
    from hsirsr.model.refsr import CrossNetHSI
    print("✅ 模块导入完成")
    
    print_step(3, "设置设备")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"✅ 使用设备: {device}")
    
    print_step(4, "加载配置")
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    print("✅ 配置加载完成")
    
    print_step(5, "创建数据集")
    train_cfg = config['train']['dataset'].copy()
    train_cfg.pop('type@', None)
    train_dataset = DaoGuSRFDataset(**train_cfg)
    print(f"✅ 训练数据集创建完成: {len(train_dataset)} 个样本")
    
    print_step(6, "创建数据加载器")
    train_loader = DataLoader(train_dataset, batch_size=1, shuffle=False, num_workers=0)
    print("✅ 数据加载器创建完成")
    
    print_step(7, "创建模型")
    model = CrossNetHSI(
        use_mask=config['module']['model']['use_mask'],
        use_pwc=config['module']['model']['use_pwc'],
        reweight=config['module']['model']['reweight']
    ).to(device)
    print(f"✅ 模型创建完成，参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    print_step(8, "创建优化器")
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
    criterion = nn.L1Loss()
    print("✅ 优化器创建完成")
    
    print_step(9, "创建保存目录")
    save_dir = Path('saved/step-by-step-training')
    save_dir.mkdir(parents=True, exist_ok=True)
    print(f"✅ 保存目录创建: {save_dir}")
    
    print_step(10, "开始训练循环")
    
    max_epochs = 10
    best_loss = float('inf')
    
    for epoch in range(1, max_epochs + 1):
        print(f"\n--- Epoch {epoch}/{max_epochs} ---")
        epoch_start = time.time()
        
        model.train()
        total_loss = 0.0
        successful_batches = 0
        
        print(f"开始处理 {len(train_loader)} 个批次...")
        
        for batch_idx, batch in enumerate(train_loader):
            batch_start = time.time()
            
            try:
                print(f"  处理批次 {batch_idx+1}/{len(train_loader)}...")
                
                # 移动数据到GPU
                hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
                hsi_hr = hsi_hr.to(device)
                hsi_lr = hsi_lr.to(device)
                hsi_rgb_lr = hsi_rgb_lr.to(device)
                rgb_hr = rgb_hr.to(device)
                
                print(f"    数据形状: HSI_LR={hsi_lr.shape}, RGB_HR={rgb_hr.shape}")
                
                # 前向传播
                optimizer.zero_grad()
                output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
                
                # 计算损失
                loss = criterion(output, hsi_hr)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
                successful_batches += 1
                
                batch_time = time.time() - batch_start
                print(f"    ✅ 损失: {loss.item():.6f}, 用时: {batch_time:.2f}s")
                
                # 每5个批次清理GPU缓存
                if batch_idx % 5 == 0:
                    torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"    ❌ 批次失败: {e}")
                continue
        
        # Epoch总结
        if successful_batches > 0:
            avg_loss = total_loss / successful_batches
        else:
            avg_loss = float('inf')
        
        epoch_time = time.time() - epoch_start
        
        print(f"\nEpoch {epoch} 完成:")
        print(f"  用时: {epoch_time:.1f}s")
        print(f"  成功批次: {successful_batches}/{len(train_loader)}")
        print(f"  平均损失: {avg_loss:.6f}")
        
        # 保存模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_loss,
                'best_loss': best_loss,
            }, save_dir / 'best_model.pth')
            print(f"  ✅ 保存最佳模型 (Loss: {best_loss:.6f})")
        
        # 每3个epoch保存检查点
        if epoch % 3 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_loss,
            }, save_dir / f'checkpoint_epoch_{epoch}.pth')
            print(f"  💾 保存检查点: epoch_{epoch}")
    
    print_step(11, "训练完成")
    print(f"✅ 最佳损失: {best_loss:.6f}")
    print(f"✅ 模型保存在: {save_dir}")
    
    # 保存最终模型
    torch.save({
        'epoch': max_epochs,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_loss': avg_loss,
        'best_loss': best_loss,
    }, save_dir / 'final_model.pth')
    
    print("\n🎉 DaoGu数据集HSI-RefSR模型训练成功完成！")

if __name__ == '__main__':
    main()
