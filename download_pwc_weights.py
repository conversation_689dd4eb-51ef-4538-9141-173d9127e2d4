#!/usr/bin/env python3
"""
PWC权重下载脚本
解决权限问题，手动下载PWC预训练权重
"""

import os
import requests
import torch
from pathlib import Path
import shutil

def create_cache_dir():
    """创建缓存目录"""
    cache_dir = Path.home() / '.cache' / 'torch' / 'hub' / 'checkpoints'
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    # 检查权限
    test_file = cache_dir / 'test_write.tmp'
    try:
        test_file.write_text('test')
        test_file.unlink()
        print(f"✅ 缓存目录可写: {cache_dir}")
        return cache_dir
    except PermissionError:
        print(f"❌ 缓存目录无写权限: {cache_dir}")
        
        # 尝试在项目目录创建
        local_cache = Path('cache/torch/hub/checkpoints')
        local_cache.mkdir(parents=True, exist_ok=True)
        print(f"✅ 使用本地缓存目录: {local_cache}")
        return local_cache

def download_pwc_weights(cache_dir):
    """下载PWC权重"""
    url = "https://github.com/sniklaus/pytorch-pwc/raw/master/network-default.pytorch"
    filename = "pwc-default"
    filepath = cache_dir / filename
    
    if filepath.exists():
        print(f"✅ PWC权重已存在: {filepath}")
        return filepath
    
    print(f"🔄 下载PWC权重...")
    print(f"  URL: {url}")
    print(f"  保存到: {filepath}")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ PWC权重下载完成: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"❌ PWC权重下载失败: {e}")
        return None

def verify_pwc_weights(filepath):
    """验证PWC权重"""
    try:
        state_dict = torch.load(filepath, map_location='cpu')
        print(f"✅ PWC权重验证成功")
        print(f"  权重键数量: {len(state_dict)}")
        
        # 打印一些关键信息
        total_params = sum(p.numel() for p in state_dict.values() if isinstance(p, torch.Tensor))
        print(f"  总参数数量: {total_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ PWC权重验证失败: {e}")
        return False

def setup_environment_variable(cache_dir):
    """设置环境变量"""
    torch_home = cache_dir.parent.parent
    print(f"\n📝 设置环境变量:")
    print(f"  TORCH_HOME={torch_home}")
    
    os.environ['TORCH_HOME'] = str(torch_home)
    
    # 创建启动脚本
    script_content = f"""@echo off
set TORCH_HOME={torch_home}
echo TORCH_HOME设置为: %TORCH_HOME%
python train_daogu_4090.py --config daogu_config_4090.yaml
pause
"""
    
    with open('start_4090_training.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 创建启动脚本: start_4090_training.bat")

def test_pwc_loading():
    """测试PWC加载"""
    print(f"\n🔍 测试PWC模型加载...")
    
    try:
        from hsirsr.model.refsr import CrossNetHSI
        
        # 尝试创建带PWC的模型
        model = CrossNetHSI(use_mask=True, use_pwc=True, reweight=False)
        print(f"✅ PWC模型创建成功")
        
        # 计算参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  模型参数数量: {total_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ PWC模型创建失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 PWC权重下载和配置")
    print("=" * 80)
    
    # 1. 创建缓存目录
    cache_dir = create_cache_dir()
    
    # 2. 下载PWC权重
    pwc_path = download_pwc_weights(cache_dir)
    if pwc_path is None:
        print("❌ PWC权重下载失败，无法继续")
        return
    
    # 3. 验证权重
    if not verify_pwc_weights(pwc_path):
        print("❌ PWC权重验证失败，无法继续")
        return
    
    # 4. 设置环境变量
    setup_environment_variable(cache_dir)
    
    # 5. 测试加载
    if test_pwc_loading():
        print("\n" + "=" * 80)
        print("🎉 PWC配置完成！")
        print("=" * 80)
        print("现在可以使用以下方式启动4090训练:")
        print("1. 运行: start_4090_training.bat")
        print("2. 或手动设置环境变量后运行:")
        print(f"   set TORCH_HOME={cache_dir.parent.parent}")
        print("   python train_daogu_4090.py --config daogu_config_4090.yaml")
        print("=" * 80)
    else:
        print("\n❌ PWC配置失败，建议使用无PWC版本:")
        print("python train_daogu_4090.py --config daogu_config.yaml")

if __name__ == '__main__':
    main()
