#!/usr/bin/env python3
"""
DaoGu数据集HSI-RefSR模型测试脚本 - 4090优化版
专为RTX 4090设计，支持原尺寸图像测试，包含完整的对齐模块分析
"""

import time
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import cv2
from scipy.io import loadmat, savemat
import yaml

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模块
from hsirsr.model.refsr import CrossNetHSI
from daogu_dataset import DaoGuSRFDataset

def print_gpu_info():
    """打印GPU信息"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU设备: {gpu_name}")
        print(f"GPU内存: {gpu_memory:.1f} GB")
        torch.cuda.empty_cache()
        return True
    else:
        print("❌ 未检测到CUDA设备")
        return False

def load_best_model(model_path='saved/daogu-4090-training/best_model.pth'):
    """加载最佳模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = CrossNetHSI(use_mask=True, use_pwc=True, reweight=False).to(device)
    
    # 加载权重
    if Path(model_path).exists():
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 模型加载成功: {model_path}")
        print(f"  训练轮数: {checkpoint.get('epoch', 'unknown')}")
        print(f"  验证损失: {checkpoint.get('loss', 'unknown')}")
    else:
        print(f"❌ 模型文件不存在: {model_path}")
        return None, None
    
    model.eval()
    return model, device

def calculate_metrics(pred, target):
    """计算评估指标"""
    # 确保数据范围在[0,1]
    pred = np.clip(pred, 0, 1)
    target = np.clip(target, 0, 1)
    
    # PSNR
    mse = np.mean((pred - target) ** 2)
    if mse == 0:
        psnr = float('inf')
    else:
        psnr = 20 * np.log10(1.0 / np.sqrt(mse))
    
    # SSIM (简化版)
    def ssim_single_channel(x, y):
        mu_x = np.mean(x)
        mu_y = np.mean(y)
        sigma_x = np.var(x)
        sigma_y = np.var(y)
        sigma_xy = np.mean((x - mu_x) * (y - mu_y))
        
        c1 = 0.01 ** 2
        c2 = 0.03 ** 2
        
        ssim = ((2 * mu_x * mu_y + c1) * (2 * sigma_xy + c2)) / \
               ((mu_x ** 2 + mu_y ** 2 + c1) * (sigma_x + sigma_y + c2))
        return ssim
    
    # 对每个波段计算SSIM然后平均
    if len(pred.shape) == 3:
        ssim_values = []
        for i in range(pred.shape[2]):
            ssim_val = ssim_single_channel(pred[:,:,i], target[:,:,i])
            ssim_values.append(ssim_val)
        ssim = np.mean(ssim_values)
    else:
        ssim = ssim_single_channel(pred, target)
    
    return psnr, ssim

def test_single_sample(model, dataset, sample_idx, device, save_dir):
    """测试单个样本"""
    print(f"\n--- 测试样本 {sample_idx+1} ---")
    
    # 获取样本名称
    sample_name = dataset.names[sample_idx]
    print(f"样本名称: {sample_name}")
    
    # 加载数据
    sample_data = dataset[sample_idx]
    hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample_data
    
    print(f"数据形状:")
    print(f"  HSI HR: {hsi_hr.shape}")
    print(f"  HSI LR: {hsi_lr.shape}")
    print(f"  HSI RGB HR: {hsi_rgb_hr.shape}")
    print(f"  HSI RGB LR: {hsi_rgb_lr.shape}")
    print(f"  RGB HR: {rgb_hr.shape}")
    print(f"  RGB LR: {rgb_lr.shape}")
    
    # 转换为tensor并添加batch维度
    hsi_hr_tensor = torch.from_numpy(hsi_hr).unsqueeze(0).to(device)
    hsi_lr_tensor = torch.from_numpy(hsi_lr).unsqueeze(0).to(device)
    hsi_rgb_lr_tensor = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).to(device)
    rgb_lr_tensor = torch.from_numpy(rgb_lr).unsqueeze(0).to(device)
    
    # 模型推理
    start_time = time.time()
    with torch.no_grad():
        # 获取完整输出（包括对齐信息）
        if hasattr(model, 'forward') and 'ref_warp' in str(model.forward.__code__.co_varnames):
            # 如果模型返回对齐信息
            output, ref_warp, flow, masks = model(hsi_lr_tensor, hsi_rgb_lr_tensor, rgb_lr_tensor)
        else:
            # 标准输出
            output = model(hsi_lr_tensor, hsi_rgb_lr_tensor, rgb_lr_tensor)
            ref_warp, flow, masks = None, None, None
    
    inference_time = time.time() - start_time
    print(f"推理时间: {inference_time:.3f}秒")
    
    # 转换回numpy
    output_np = output.squeeze(0).cpu().numpy()  # [1, C, H, W] -> [C, H, W]
    hsi_hr_np = hsi_hr_tensor.squeeze(0).cpu().numpy()
    hsi_rgb_lr_np = hsi_rgb_lr_tensor.squeeze(0).cpu().numpy()
    rgb_lr_np = rgb_lr_tensor.squeeze(0).cpu().numpy()
    
    # 转换为HWC格式用于可视化
    def chw_to_hwc(img):
        if len(img.shape) == 4:  # [1, C, H, W]
            return img[0].transpose(1, 2, 0)
        elif len(img.shape) == 3:  # [C, H, W]
            return img.transpose(1, 2, 0)
        return img
    
    output_hwc = chw_to_hwc(output_np)
    hsi_hr_hwc = chw_to_hwc(hsi_hr_np)
    hsi_rgb_lr_hwc = chw_to_hwc(hsi_rgb_lr_np)
    rgb_lr_hwc = chw_to_hwc(rgb_lr_np)
    
    print(f"输出形状: {output_hwc.shape}")
    
    # 计算评估指标
    psnr, ssim = calculate_metrics(output_hwc, hsi_hr_hwc)
    print(f"PSNR: {psnr:.2f} dB")
    print(f"SSIM: {ssim:.4f}")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'样本 {sample_idx+1}: {sample_name}\nPSNR: {psnr:.2f}dB, SSIM: {ssim:.4f}', fontsize=16)
    
    # 选择几个代表性波段进行可视化
    bands_to_show = [50, 100, 150]  # 选择不同的波段
    
    for i, band in enumerate(bands_to_show):
        if band < output_hwc.shape[2]:
            # 第一行：输入和输出
            axes[0, i].imshow(hsi_hr_hwc[:,:,band], cmap='viridis')
            axes[0, i].set_title(f'Ground Truth (Band {band})')
            axes[0, i].axis('off')
            
            # 第二行：预测结果
            axes[1, i].imshow(output_hwc[:,:,band], cmap='viridis')
            axes[1, i].set_title(f'Prediction (Band {band})')
            axes[1, i].axis('off')
    
    plt.tight_layout()
    
    # 保存结果
    sample_save_dir = save_dir / f'sample_{sample_idx+1}_{sample_name}'
    sample_save_dir.mkdir(exist_ok=True)
    
    plt.savefig(sample_save_dir / 'visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存数值结果
    results = {
        'sample_name': sample_name,
        'sample_idx': sample_idx,
        'psnr': float(psnr),
        'ssim': float(ssim),
        'inference_time': float(inference_time),
        'input_shape': hsi_lr.shape,
        'output_shape': output_hwc.shape
    }
    
    # 保存HSI结果为.mat文件
    savemat(sample_save_dir / 'hsi_result.mat', {
        'ground_truth': hsi_hr_hwc,
        'prediction': output_hwc,
        'input_lr': chw_to_hwc(hsi_lr),
        'metrics': results
    })
    
    print(f"✅ 结果已保存到: {sample_save_dir}")
    
    return results

def test_all_samples():
    """测试所有样本"""
    print("🔍 开始原尺寸图像全面测试")
    print("=" * 80)
    
    # 检查GPU
    if not print_gpu_info():
        return
    
    # 加载模型
    model, device = load_best_model()
    if model is None:
        print("❌ 模型加载失败，请先训练模型")
        return
    
    # 创建数据集
    dataset = DaoGuSRFDataset(
        root='data/daogu_processed',
        input='img1',
        ref='img2', 
        names_path='test.txt',
        sf=4,
        crop_size=None,  # 不裁切，使用原尺寸
        use_cache=False
    )
    
    print(f"📊 测试数据集: {len(dataset)} 个样本")
    
    # 创建保存目录
    save_dir = Path('original_size_4090_results')
    save_dir.mkdir(exist_ok=True)
    
    # 存储所有结果
    all_results = []
    
    print("\n🚀 开始逐一测试所有样本...")
    
    for sample_idx in range(len(dataset)):
        try:
            results = test_single_sample(model, dataset, sample_idx, device, save_dir)
            all_results.append(results)
            
            # 清理GPU内存
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"❌ 样本 {sample_idx+1} 测试失败: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 计算总体统计
    if all_results:
        avg_psnr = np.mean([r['psnr'] for r in all_results])
        avg_ssim = np.mean([r['ssim'] for r in all_results])
        avg_time = np.mean([r['inference_time'] for r in all_results])
        
        print(f"\n📈 总体测试结果:")
        print(f"  测试样本数: {len(all_results)}")
        print(f"  平均PSNR: {avg_psnr:.2f} dB")
        print(f"  平均SSIM: {avg_ssim:.4f}")
        print(f"  平均推理时间: {avg_time:.3f}秒")
        
        # 保存总体结果
        summary = {
            'total_samples': len(all_results),
            'avg_psnr': float(avg_psnr),
            'avg_ssim': float(avg_ssim),
            'avg_inference_time': float(avg_time),
            'all_results': all_results
        }
        
        import json
        with open(save_dir / 'test_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试完成！结果保存在: {save_dir}")
    else:
        print("❌ 没有成功测试的样本")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 DaoGu数据集HSI-RefSR模型测试 - 4090优化版")
    print("=" * 80)
    
    test_all_samples()

if __name__ == '__main__':
    main()
