#!/usr/bin/env python3
"""
DaoGu数据集HSI-RefSR模型全面测试脚本
包含定量指标计算、可视化结果生成、多模型对比等
"""

import os
import time
import yaml
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from torch.utils.data import DataLoader
from skimage.metrics import peak_signal_noise_ratio, structural_similarity
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模块
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

def calculate_metrics(pred, target):
    """计算PSNR和SSIM指标"""
    if isinstance(pred, torch.Tensor):
        pred = pred.detach().cpu().numpy()
    if isinstance(target, torch.Tensor):
        target = target.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    pred = np.clip(pred, 0, 1)
    target = np.clip(target, 0, 1)
    
    # 计算PSNR
    psnr = peak_signal_noise_ratio(target, pred, data_range=1.0)
    
    # 计算SSIM
    ssim = structural_similarity(target, pred, data_range=1.0)
    
    return psnr, ssim

def load_model(model_path, device):
    """加载训练好的模型"""
    print(f"加载模型: {model_path}")
    
    # 创建模型
    model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"✅ 模型加载完成")
    if 'best_loss' in checkpoint:
        print(f"   训练损失: {checkpoint.get('train_loss', 'N/A'):.6f}")
        print(f"   最佳损失: {checkpoint['best_loss']:.6f}")
    
    return model

def test_model(model, test_loader, device, model_name):
    """测试单个模型"""
    print(f"\n{'='*60}")
    print(f"测试模型: {model_name}")
    print(f"{'='*60}")
    
    model.eval()
    all_psnr = []
    all_ssim = []
    all_losses = []
    test_results = []
    
    criterion = nn.L1Loss()
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            try:
                # 移动数据到GPU
                hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
                hsi_hr = hsi_hr.to(device)
                hsi_lr = hsi_lr.to(device)
                hsi_rgb_lr = hsi_rgb_lr.to(device)
                rgb_hr = rgb_hr.to(device)
                
                # 前向传播
                output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
                
                # 计算损失
                loss = criterion(output, hsi_hr)
                all_losses.append(loss.item())
                
                # 计算指标 (选择中间波段)
                mid_band = output.shape[2] // 2
                pred_band = output[0, 0, mid_band].cpu().numpy()
                target_band = hsi_hr[0, 0, mid_band].cpu().numpy()
                
                psnr, ssim = calculate_metrics(pred_band, target_band)
                all_psnr.append(psnr)
                all_ssim.append(ssim)
                
                # 保存测试结果用于可视化
                test_results.append({
                    'sample_idx': batch_idx,
                    'hsi_hr': hsi_hr[0, 0].cpu().numpy(),
                    'hsi_lr': hsi_lr[0, 0].cpu().numpy(),
                    'output': output[0, 0].cpu().numpy(),
                    'rgb_hr': rgb_hr[0].cpu().numpy(),
                    'psnr': psnr,
                    'ssim': ssim,
                    'loss': loss.item()
                })
                
                print(f"  样本 {batch_idx+1}: PSNR={psnr:.2f}dB, SSIM={ssim:.4f}, Loss={loss.item():.6f}")
                
            except Exception as e:
                print(f"  ❌ 样本 {batch_idx+1} 测试失败: {e}")
                continue
    
    # 计算统计结果
    if all_psnr:
        avg_psnr = np.mean(all_psnr)
        std_psnr = np.std(all_psnr)
        avg_ssim = np.mean(all_ssim)
        std_ssim = np.std(all_ssim)
        avg_loss = np.mean(all_losses)
        
        print(f"\n📊 {model_name} 测试结果:")
        print(f"   PSNR: {avg_psnr:.2f} ± {std_psnr:.2f} dB")
        print(f"   SSIM: {avg_ssim:.4f} ± {std_ssim:.4f}")
        print(f"   Loss: {avg_loss:.6f}")
        print(f"   测试样本数: {len(all_psnr)}")
        
        return {
            'model_name': model_name,
            'avg_psnr': avg_psnr,
            'std_psnr': std_psnr,
            'avg_ssim': avg_ssim,
            'std_ssim': std_ssim,
            'avg_loss': avg_loss,
            'test_results': test_results,
            'all_psnr': all_psnr,
            'all_ssim': all_ssim
        }
    else:
        print(f"❌ {model_name} 测试失败")
        return None

def create_performance_comparison(results_list, save_dir):
    """创建性能对比图"""
    plt.figure(figsize=(15, 5))
    
    model_names = [r['model_name'] for r in results_list if r is not None]
    psnr_means = [r['avg_psnr'] for r in results_list if r is not None]
    psnr_stds = [r['std_psnr'] for r in results_list if r is not None]
    ssim_means = [r['avg_ssim'] for r in results_list if r is not None]
    ssim_stds = [r['std_ssim'] for r in results_list if r is not None]
    losses = [r['avg_loss'] for r in results_list if r is not None]
    
    # PSNR对比
    plt.subplot(1, 3, 1)
    bars = plt.bar(model_names, psnr_means, yerr=psnr_stds, capsize=5, alpha=0.7, color='skyblue')
    plt.title('PSNR 对比', fontsize=14, fontweight='bold')
    plt.ylabel('PSNR (dB)')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, mean, std in zip(bars, psnr_means, psnr_stds):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.1,
                f'{mean:.2f}±{std:.2f}', ha='center', va='bottom', fontsize=10)
    
    # SSIM对比
    plt.subplot(1, 3, 2)
    bars = plt.bar(model_names, ssim_means, yerr=ssim_stds, capsize=5, alpha=0.7, color='orange')
    plt.title('SSIM 对比', fontsize=14, fontweight='bold')
    plt.ylabel('SSIM')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, mean, std in zip(bars, ssim_means, ssim_stds):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.01,
                f'{mean:.4f}±{std:.4f}', ha='center', va='bottom', fontsize=10)
    
    # Loss对比
    plt.subplot(1, 3, 3)
    bars = plt.bar(model_names, losses, alpha=0.7, color='lightgreen')
    plt.title('Loss 对比', fontsize=14, fontweight='bold')
    plt.ylabel('L1 Loss')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, loss in zip(bars, losses):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                f'{loss:.6f}', ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'model_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 模型对比图保存完成")

def create_sample_visualization(sample, save_dir, filename):
    """创建单个样本的可视化"""
    hsi_hr = sample['hsi_hr']
    hsi_lr = sample['hsi_lr'] 
    output = sample['output']
    
    # 选择几个代表性波段进行可视化
    bands_to_show = [50, 136, 220]  # 对应不同波长区域
    
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle(f'{filename} - PSNR: {sample["psnr"]:.2f}dB, SSIM: {sample["ssim"]:.4f}', 
                 fontsize=16, fontweight='bold')
    
    for i, band in enumerate(bands_to_show):
        if band < hsi_hr.shape[0]:
            # 高分辨率真值
            im1 = axes[i, 0].imshow(hsi_hr[band], cmap='viridis', vmin=0, vmax=1)
            axes[i, 0].set_title(f'HR Ground Truth\nBand {band+1}')
            axes[i, 0].axis('off')
            plt.colorbar(im1, ax=axes[i, 0], fraction=0.046)
            
            # 低分辨率输入
            im2 = axes[i, 1].imshow(hsi_lr[band], cmap='viridis', vmin=0, vmax=1)
            axes[i, 1].set_title(f'LR Input\nBand {band+1}')
            axes[i, 1].axis('off')
            plt.colorbar(im2, ax=axes[i, 1], fraction=0.046)
            
            # 模型输出
            im3 = axes[i, 2].imshow(output[band], cmap='viridis', vmin=0, vmax=1)
            axes[i, 2].set_title(f'SR Output\nBand {band+1}')
            axes[i, 2].axis('off')
            plt.colorbar(im3, ax=axes[i, 2], fraction=0.046)
            
            # 误差图
            error = np.abs(hsi_hr[band] - output[band])
            im4 = axes[i, 3].imshow(error, cmap='hot', vmin=0, vmax=0.2)
            axes[i, 3].set_title(f'Error Map\nBand {band+1}')
            axes[i, 3].axis('off')
            plt.colorbar(im4, ax=axes[i, 3], fraction=0.046)
    
    plt.tight_layout()
    plt.savefig(save_dir / f'{filename}_detailed.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主测试函数"""
    print("🚀 DaoGu数据集HSI-RefSR模型全面测试")
    print("=" * 80)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载配置和测试数据
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    test_cfg = config['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    print(f"测试数据集: {len(test_dataset)} 个样本")
    
    # 创建结果保存目录
    save_dir = Path('test_results')
    save_dir.mkdir(exist_ok=True)
    
    # 测试多个模型
    model_paths = [
        ('saved/step-by-step-training/best_model.pth', 'Step-by-Step Best'),
        ('saved/minimal-training/minimal_model.pth', 'Minimal Training'),
        ('saved/daogu-train-only/best_model.pth', 'Train-Only Best'),
    ]
    
    results_list = []
    
    for model_path, model_name in model_paths:
        if Path(model_path).exists():
            try:
                model = load_model(model_path, device)
                result = test_model(model, test_loader, device, model_name)
                if result:
                    results_list.append(result)
                del model
                torch.cuda.empty_cache()
            except Exception as e:
                print(f"❌ 模型 {model_name} 测试失败: {e}")
                results_list.append(None)
        else:
            print(f"⚠️ 模型文件不存在: {model_path}")
            results_list.append(None)
    
    # 生成可视化结果
    if any(r is not None for r in results_list):
        create_performance_comparison(results_list, save_dir)
        
        # 最佳模型的详细结果可视化
        best_result = max([r for r in results_list if r is not None], key=lambda x: x['avg_psnr'])
        
        # 选择几个代表性样本进行可视化
        sample_indices = [0, 1, 2] if len(best_result['test_results']) >= 3 else list(range(len(best_result['test_results'])))
        
        for idx in sample_indices:
            if idx < len(best_result['test_results']):
                sample = best_result['test_results'][idx]
                create_sample_visualization(sample, save_dir, f"sample_{idx+1}")
        
        print("✅ 样本可视化完成")
        
        # 生成测试报告
        with open(save_dir / 'test_report.txt', 'w', encoding='utf-8') as f:
            f.write("DaoGu数据集HSI-RefSR模型测试报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试样本数: {len(test_dataset)}\n")
            f.write(f"数据集: DaoGu SWIR (273波段, 996-2501nm)\n\n")
            
            for result in results_list:
                if result:
                    f.write(f"模型: {result['model_name']}\n")
                    f.write(f"  PSNR: {result['avg_psnr']:.2f} ± {result['std_psnr']:.2f} dB\n")
                    f.write(f"  SSIM: {result['avg_ssim']:.4f} ± {result['std_ssim']:.4f}\n")
                    f.write(f"  Loss: {result['avg_loss']:.6f}\n\n")
        
        print(f"\n🎉 全面测试完成！")
        print(f"📊 结果保存在: {save_dir}")
        print(f"📈 可视化图表: {save_dir}/model_comparison.png")
        print(f"📄 测试报告: {save_dir}/test_report.txt")
    else:
        print("❌ 所有模型测试都失败了")

if __name__ == '__main__':
    main()
