"""
DaoGu数据集处理脚本
将ENVI格式的HSI数据转换为HSI-RefSR模型可用的格式
"""

import os
import numpy as np
import cv2
from pathlib import Path
import struct
from scipy.io import savemat
from imageio import imsave
import random
from tqdm import tqdm

# 光谱响应函数 (SRF) - 用于HSI到RGB转换
srf = np.array([
    [0.005, 0.007, 0.012, 0.015, 0.023, 0.025, 0.030, 0.026, 0.024, 0.019,
     0.010, 0.004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0.000, 0.000, 0.000, 0.000, 0.000, 0.001, 0.002, 0.003, 0.005, 0.007,
     0.012, 0.013, 0.015, 0.016, 0.017, 0.02, 0.013, 0.011, 0.009, 0.005,
     0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.002, 0.002, 0.003],
    [0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000,
     0.000, 0.000, 0.000, 0.000, 0.000, 0.001, 0.003, 0.010, 0.012, 0.013, 0.022,
     0.020, 0.020, 0.018, 0.017, 0.016, 0.016, 0.014, 0.014, 0.013]
], dtype=np.float32)


def read_envi_header(hdr_file):
    """
    读取ENVI头文件，提取关键信息
    """
    header_info = {}
    
    with open(hdr_file, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            if key in ['samples', 'bands', 'lines', 'data type', 'header offset', 'byte order']:
                header_info[key] = int(value)
            elif key == 'interleave':
                header_info[key] = value
            elif key == 'Wavelength':
                # 处理波长信息
                wavelengths = []
                i = lines.index(line + '\n')
                while i < len(lines):
                    line_content = lines[i].strip()
                    if line_content.endswith('}'):
                        # 提取最后一行的数字
                        numbers = line_content.replace('}', '').split(',')
                        for num in numbers:
                            if num.strip():
                                try:
                                    wavelengths.append(float(num.strip()))
                                except:
                                    pass
                        break
                    elif line_content.startswith('{'):
                        # 提取第一行的数字
                        numbers = line_content.replace('{', '').split(',')
                        for num in numbers:
                            if num.strip():
                                try:
                                    wavelengths.append(float(num.strip()))
                                except:
                                    pass
                    else:
                        # 中间行
                        numbers = line_content.split(',')
                        for num in numbers:
                            if num.strip():
                                try:
                                    wavelengths.append(float(num.strip()))
                                except:
                                    pass
                    i += 1
                header_info['wavelengths'] = wavelengths
    
    return header_info


def read_envi_data(dat_file, hdr_file):
    """
    读取ENVI格式的HSI数据
    """
    # 读取头文件信息
    header = read_envi_header(hdr_file)
    
    samples = header['samples']  # 宽度
    lines = header['lines']      # 高度  
    bands = header['bands']      # 波段数
    data_type = header['data type']
    interleave = header['interleave']
    
    # 根据数据类型确定numpy数据类型
    dtype_map = {
        1: np.uint8,
        2: np.int16,
        3: np.int32,
        4: np.float32,
        5: np.float64,
        12: np.uint16
    }
    
    dtype = dtype_map.get(data_type, np.float32)
    
    # 读取二进制数据
    with open(dat_file, 'rb') as f:
        data = np.frombuffer(f.read(), dtype=dtype)
    
    # 根据交错格式重塑数据
    if interleave.lower() == 'bil':  # Band Interleaved by Line
        data = data.reshape((lines, bands, samples))
        data = data.transpose(0, 2, 1)  # 转换为 (height, width, bands)
    elif interleave.lower() == 'bip':  # Band Interleaved by Pixel
        data = data.reshape((lines, samples, bands))
    elif interleave.lower() == 'bsq':  # Band Sequential
        data = data.reshape((bands, lines, samples))
        data = data.transpose(1, 2, 0)  # 转换为 (height, width, bands)
    
    return data.astype(np.float32), header


def minmax_normalize(img):
    """
    最小-最大归一化
    """
    img_min = np.min(img)
    img_max = np.max(img)
    if img_max > img_min:
        return (img - img_min) / (img_max - img_min)
    else:
        return img


def hsi_to_rgb_simple(hsi_data):
    """
    使用简单的波段选择将HSI转换为RGB
    选择接近红、绿、蓝波长的波段
    """
    # 确保HSI数据是归一化的
    hsi_normalized = minmax_normalize(hsi_data)

    # 对于SWIR数据(996-2501nm)，选择合适的波段组合
    # 由于这是近红外数据，我们需要映射到可见光范围
    bands = hsi_normalized.shape[2]

    # 选择三个波段作为RGB
    # 使用前、中、后三个区域的波段
    r_band = bands // 4      # 约1/4位置
    g_band = bands // 2      # 中间位置
    b_band = 3 * bands // 4  # 约3/4位置

    rgb = np.stack([
        hsi_normalized[:, :, r_band],
        hsi_normalized[:, :, g_band],
        hsi_normalized[:, :, b_band]
    ], axis=2)

    return rgb


def hsi_to_rgb(hsi_data, srf_matrix):
    """
    使用光谱响应函数将HSI转换为RGB
    """
    # 对于SWIR数据，直接使用简单的波段选择
    # 因为SRF是为可见光设计的，不适用于SWIR
    return hsi_to_rgb_simple(hsi_data)


def apply_random_offset(img, max_offset=20):
    """
    对图像应用随机偏移
    """
    h, w = img.shape[:2]
    
    # 生成随机偏移量
    offset_x = random.randint(-max_offset, max_offset)
    offset_y = random.randint(-max_offset, max_offset)
    
    # 创建变换矩阵
    M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
    
    # 应用偏移
    if len(img.shape) == 3:
        shifted = cv2.warpAffine(img, M, (w, h), borderMode=cv2.BORDER_REFLECT)
    else:
        shifted = cv2.warpAffine(img, M, (w, h), borderMode=cv2.BORDER_REFLECT)
    
    return shifted, (offset_x, offset_y)


def downsample_image(img, scale_factor=4):
    """
    下采样图像
    """
    h, w = img.shape[:2]
    new_h, new_w = h // scale_factor, w // scale_factor

    if len(img.shape) == 3:
        downsampled = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
    else:
        downsampled = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)

    return downsampled


def find_hsi_files(root_dir):
    """
    查找所有HSI数据文件
    """
    hsi_files = []
    root_path = Path(root_dir)

    for exp_dir in root_path.glob("实验*"):
        if exp_dir.is_dir():
            for sample_dir in exp_dir.iterdir():
                if sample_dir.is_dir():
                    capture_dir = sample_dir / "capture"
                    if capture_dir.exists():
                        # 查找REFLECTANCE文件
                        for dat_file in capture_dir.glob("REFLECTANCE_*.dat"):
                            hdr_file = dat_file.with_suffix('.hdr')
                            if hdr_file.exists():
                                hsi_files.append({
                                    'dat_file': str(dat_file),
                                    'hdr_file': str(hdr_file),
                                    'sample_name': sample_dir.name,
                                    'exp_name': exp_dir.name
                                })

    return hsi_files


def create_dataset_structure(output_dir):
    """
    创建HSI-RefSR所需的数据集目录结构
    """
    output_path = Path(output_dir)

    # 创建目录结构
    dirs_to_create = [
        'img1_hsi/HR',
        'img2_hsi/HR',
        'img1/HR',
        'img2/HR'
    ]

    for dir_name in dirs_to_create:
        (output_path / dir_name).mkdir(parents=True, exist_ok=True)

    print(f"Created dataset structure in {output_dir}")


def process_single_hsi(hsi_info, output_dir, scale_factor=4, max_offset=20):
    """
    处理单个HSI文件
    """
    try:
        # 读取HSI数据
        print(f"Processing {hsi_info['sample_name']}...")
        hsi_data, header = read_envi_data(hsi_info['dat_file'], hsi_info['hdr_file'])

        print(f"HSI shape: {hsi_data.shape}")
        print(f"Bands: {header.get('bands', 'unknown')}")

        # 归一化HSI数据
        hsi_normalized = minmax_normalize(hsi_data)

        # 转换为RGB
        rgb_from_hsi = hsi_to_rgb(hsi_normalized, srf)

        # 应用偏移操作生成"真实"RGB图像
        rgb_offset, offset_info = apply_random_offset(rgb_from_hsi, max_offset)

        # 下采样HSI生成的RGB作为低分辨率输入
        rgb_lr = downsample_image(rgb_from_hsi, scale_factor)

        # 生成文件名，使用英文避免编码问题
        exp_num = hsi_info['exp_name'].replace('实验', 'exp')
        base_name = f"{exp_num}_{hsi_info['sample_name']}"

        # 保存HSI数据 (作为.mat文件)
        hsi_mat_path1 = Path(output_dir) / 'img1_hsi' / 'HR' / f"{base_name}.mat"
        hsi_mat_path2 = Path(output_dir) / 'img2_hsi' / 'HR' / f"{base_name}.mat"

        # 保存HSI数据，img1为原始，img2为偏移后的HSI（模拟不同视角）
        savemat(str(hsi_mat_path1), {'gt': hsi_normalized})

        # 为img2创建轻微变化的HSI数据（模拟不同相机的HSI）
        hsi_variant = hsi_normalized + np.random.normal(0, 0.01, hsi_normalized.shape)
        hsi_variant = np.clip(hsi_variant, 0, 1)
        savemat(str(hsi_mat_path2), {'gt': hsi_variant})

        # 保存RGB图像
        rgb_path1 = Path(output_dir) / 'img1' / 'HR' / f"{base_name}.png"
        rgb_path2 = Path(output_dir) / 'img2' / 'HR' / f"{base_name}.png"

        # img1: 下采样的RGB (低分辨率)
        # img2: 偏移的RGB (高分辨率，模拟真实相机拍摄)
        rgb_lr_uint8 = (rgb_lr * 255).astype(np.uint8)
        rgb_offset_uint8 = (rgb_offset * 255).astype(np.uint8)

        imsave(str(rgb_path1), rgb_lr_uint8)
        imsave(str(rgb_path2), rgb_offset_uint8)

        print(f"Saved: {base_name}")
        print(f"  HSI shape: {hsi_normalized.shape}")
        print(f"  RGB LR shape: {rgb_lr.shape}")
        print(f"  RGB HR shape: {rgb_offset.shape}")
        print(f"  Offset: {offset_info}")

        return True

    except Exception as e:
        print(f"Error processing {hsi_info['sample_name']}: {str(e)}")
        return False


def create_name_files(output_dir, hsi_files):
    """
    创建训练和测试文件名列表
    """
    output_path = Path(output_dir)

    # 生成所有样本名称，使用英文名称避免编码问题
    sample_names = []
    for i, hsi_info in enumerate(hsi_files):
        # 使用简单的英文命名
        exp_num = hsi_info['exp_name'].replace('实验', 'exp')
        base_name = f"{exp_num}_{hsi_info['sample_name']}"
        sample_names.append(base_name)

    # 分割训练和测试集 (80% 训练, 20% 测试)
    random.shuffle(sample_names)
    split_idx = int(len(sample_names) * 0.8)

    train_names = sample_names[:split_idx]
    test_names = sample_names[split_idx:]

    # 保存文件名列表，使用UTF-8编码
    with open(output_path / 'train.txt', 'w', encoding='utf-8') as f:
        for name in train_names:
            f.write(f"{name}\n")

    with open(output_path / 'test.txt', 'w', encoding='utf-8') as f:
        for name in test_names:
            f.write(f"{name}\n")

    print(f"Created train.txt with {len(train_names)} samples")
    print(f"Created test.txt with {len(test_names)} samples")


def main():
    """
    主处理函数
    """
    # 设置路径
    daogu_dir = "DaoGu"
    output_dir = "data/daogu_processed"

    print("=== DaoGu数据集处理 ===")

    # 查找所有HSI文件
    print("1. 查找HSI文件...")
    hsi_files = find_hsi_files(daogu_dir)
    print(f"找到 {len(hsi_files)} 个HSI文件")

    if len(hsi_files) == 0:
        print("未找到HSI文件，请检查数据路径")
        return

    # 创建输出目录结构
    print("2. 创建数据集目录结构...")
    create_dataset_structure(output_dir)

    # 处理每个HSI文件
    print("3. 处理HSI文件...")
    successful_files = []

    for hsi_info in tqdm(hsi_files, desc="Processing HSI files"):
        if process_single_hsi(hsi_info, output_dir):
            successful_files.append(hsi_info)

    print(f"成功处理 {len(successful_files)} / {len(hsi_files)} 个文件")

    # 创建训练/测试文件列表
    if successful_files:
        print("4. 创建训练/测试文件列表...")
        create_name_files(output_dir, successful_files)

    print("=== 处理完成 ===")
    print(f"数据集保存在: {output_dir}")
    print("可以使用以下配置运行模型:")
    print(f"  root: {output_dir}")
    print("  input: img1")
    print("  ref: img2")


if __name__ == "__main__":
    main()
