#!/usr/bin/env python3
"""
快速训练脚本 - 带详细调试信息
"""

import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
import numpy as np

print("🚀 开始快速训练...")

# 导入必要的模块
print("导入模块...")
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI
print("✅ 模块导入完成")

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载配置
print("加载配置...")
with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)
print("✅ 配置加载完成")

# 创建数据集
print("创建数据集...")
train_cfg = config['train']['dataset'].copy()
train_cfg.pop('type@', None)
train_dataset = DaoGuSRFDataset(**train_cfg)

test_cfg = config['test']['dataset'].copy()
test_cfg.pop('type@', None)
test_dataset = DaoGuSRFDataset(**test_cfg)

print(f"✅ 数据集创建完成 - 训练: {len(train_dataset)}, 测试: {len(test_dataset)}")

# 创建数据加载器
print("创建数据加载器...")
train_loader = DataLoader(
    train_dataset,
    batch_size=1,
    shuffle=True,
    num_workers=0,
    pin_memory=True
)

val_loader = DataLoader(
    test_dataset,
    batch_size=1,
    shuffle=False,
    num_workers=0,
    pin_memory=True
)
print("✅ 数据加载器创建完成")

# 创建模型
print("创建模型...")
model = CrossNetHSI(
    use_mask=config['module']['model']['use_mask'],
    use_pwc=config['module']['model']['use_pwc'],
    reweight=config['module']['model']['reweight']
).to(device)

total_params = sum(p.numel() for p in model.parameters())
print(f"✅ 模型创建完成，参数数量: {total_params:,}")

# 创建优化器和损失函数
print("创建优化器...")
optimizer = optim.AdamW(
    model.parameters(),
    lr=config['module']['optimizer']['lr'],
    weight_decay=config['module']['optimizer']['weight_decay']
)

criterion = nn.L1Loss()
print("✅ 优化器创建完成")

# 创建保存目录
save_dir = Path('saved/daogu-quick-training')
save_dir.mkdir(parents=True, exist_ok=True)
print(f"✅ 保存目录创建: {save_dir}")

# 开始训练
print("\n" + "="*80)
print("🚀 开始训练循环")
print("="*80)

max_epochs = 5  # 只训练5个epoch用于测试
best_psnr = 0.0

for epoch in range(1, max_epochs + 1):
    print(f"\n=== Epoch {epoch}/{max_epochs} ===")
    start_time = time.time()
    
    # 训练
    model.train()
    total_loss = 0.0
    num_batches = len(train_loader)
    
    print(f"开始训练，共 {num_batches} 个批次...")
    
    for batch_idx, batch in enumerate(train_loader):
        print(f"  处理批次 {batch_idx+1}/{num_batches}...")
        
        # 移动数据到GPU
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
        hsi_hr = hsi_hr.to(device)
        hsi_lr = hsi_lr.to(device)
        hsi_rgb_lr = hsi_rgb_lr.to(device)
        rgb_hr = rgb_hr.to(device)
        
        print(f"    数据移动到GPU完成")
        
        # 前向传播
        optimizer.zero_grad()
        print(f"    开始前向传播...")
        output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
        print(f"    前向传播完成，输出形状: {output.shape}")
        
        # 计算损失
        loss = criterion(output, hsi_hr)
        print(f"    损失计算完成: {loss.item():.6f}")
        
        # 反向传播
        print(f"    开始反向传播...")
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        print(f"    反向传播完成")
        
        total_loss += loss.item()
        
        # 每5个批次打印一次
        if batch_idx % 5 == 0:
            print(f"    ✅ Batch {batch_idx+1}: Loss={loss.item():.6f}")
        
        # 只训练前10个批次用于快速测试
        if batch_idx >= 9:
            print(f"    快速测试模式：只训练前10个批次")
            break
    
    avg_loss = total_loss / min(10, num_batches)
    epoch_time = time.time() - start_time
    
    print(f"Epoch {epoch} 完成 (用时: {epoch_time:.1f}s)")
    print(f"平均训练损失: {avg_loss:.6f}")
    
    # 简单验证
    print("开始验证...")
    model.eval()
    val_loss = 0.0
    val_batches = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            loss = criterion(output, hsi_hr)
            val_loss += loss.item()
            val_batches += 1
            
            # 只验证前3个批次
            if batch_idx >= 2:
                break
    
    avg_val_loss = val_loss / val_batches
    print(f"平均验证损失: {avg_val_loss:.6f}")
    
    # 保存模型
    if epoch == 1 or avg_val_loss < best_psnr:
        best_psnr = avg_val_loss
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': avg_loss,
            'val_loss': avg_val_loss,
        }, save_dir / 'best_model.pth')
        print(f"✅ 保存最佳模型")
    
    print("-" * 80)

print(f"\n🎉 快速训练完成！")
print(f"模型保存在: {save_dir}")
print("训练成功，可以开始完整训练！")
