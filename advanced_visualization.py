#!/usr/bin/env python3
"""
高级可视化脚本 - 生成更多详细的测试结果图表
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import torch
import yaml
from torch.utils.data import DataLoader
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI
import cv2

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def load_best_model():
    """加载最佳模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
    
    checkpoint = torch.load('saved/step-by-step-training/best_model.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, device

def create_spectral_analysis(save_dir):
    """创建光谱分析图"""
    print("生成光谱分析图...")
    
    # 加载模型和数据
    model, device = load_best_model()
    
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    test_cfg = config['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 选择一个样本进行光谱分析
    sample_batch = next(iter(test_loader))
    hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample_batch
    
    hsi_hr = hsi_hr.to(device)
    hsi_lr = hsi_lr.to(device)
    hsi_rgb_lr = hsi_rgb_lr.to(device)
    rgb_hr = rgb_hr.to(device)
    
    with torch.no_grad():
        output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
    
    # 转换为numpy
    hsi_hr_np = hsi_hr[0, 0].cpu().numpy()  # [273, H, W]
    hsi_lr_np = hsi_lr[0, 0].cpu().numpy()
    output_np = output[0, 0].cpu().numpy()
    
    # 选择中心像素进行光谱分析
    h, w = hsi_hr_np.shape[1], hsi_hr_np.shape[2]
    center_y, center_x = h // 2, w // 2
    
    # 提取光谱曲线
    spectrum_hr = hsi_hr_np[:, center_y, center_x]
    spectrum_lr = hsi_lr_np[:, center_y, center_x]
    spectrum_sr = output_np[:, center_y, center_x]
    
    # 波长信息 (996-2501nm, 273个波段)
    wavelengths = np.linspace(996, 2501, 273)
    
    # 创建光谱对比图
    plt.figure(figsize=(15, 10))
    
    # 主光谱图
    plt.subplot(2, 2, 1)
    plt.plot(wavelengths, spectrum_hr, 'b-', label='HR Ground Truth', linewidth=2)
    plt.plot(wavelengths, spectrum_sr, 'r--', label='SR Output', linewidth=2)
    plt.plot(wavelengths, spectrum_lr, 'g:', label='LR Input', linewidth=1, alpha=0.7)
    plt.xlabel('波长 (nm)')
    plt.ylabel('反射率')
    plt.title('中心像素光谱对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 误差光谱
    plt.subplot(2, 2, 2)
    error_spectrum = np.abs(spectrum_hr - spectrum_sr)
    plt.plot(wavelengths, error_spectrum, 'r-', linewidth=2)
    plt.xlabel('波长 (nm)')
    plt.ylabel('绝对误差')
    plt.title('光谱重建误差')
    plt.grid(True, alpha=0.3)
    
    # 相关性分析
    plt.subplot(2, 2, 3)
    plt.scatter(spectrum_hr, spectrum_sr, alpha=0.6, s=20)
    min_val = min(spectrum_hr.min(), spectrum_sr.min())
    max_val = max(spectrum_hr.max(), spectrum_sr.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    plt.xlabel('HR Ground Truth')
    plt.ylabel('SR Output')
    plt.title('光谱值相关性')
    plt.grid(True, alpha=0.3)
    
    # 计算相关系数
    correlation = np.corrcoef(spectrum_hr, spectrum_sr)[0, 1]
    plt.text(0.05, 0.95, f'相关系数: {correlation:.4f}', 
             transform=plt.gca().transAxes, fontsize=12, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 波段重要性分析
    plt.subplot(2, 2, 4)
    band_errors = np.mean(np.abs(hsi_hr_np - output_np), axis=(1, 2))
    plt.plot(wavelengths, band_errors, 'purple', linewidth=2)
    plt.xlabel('波长 (nm)')
    plt.ylabel('平均绝对误差')
    plt.title('各波段重建误差')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'spectral_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 光谱分析图保存完成")

def create_multi_sample_comparison(save_dir):
    """创建多样本对比图"""
    print("生成多样本对比图...")
    
    model, device = load_best_model()
    
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    test_cfg = config['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # 收集所有样本的结果
    all_results = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            
            # 选择中间波段进行可视化
            mid_band = output.shape[2] // 2
            
            all_results.append({
                'sample_idx': batch_idx,
                'hsi_hr': hsi_hr[0, 0, mid_band].cpu().numpy(),
                'hsi_lr': hsi_lr[0, 0, mid_band].cpu().numpy(),
                'output': output[0, 0, mid_band].cpu().numpy(),
                'rgb_hr': rgb_hr[0].permute(1, 2, 0).cpu().numpy()
            })
    
    # 创建网格显示
    n_samples = len(all_results)
    fig, axes = plt.subplots(4, n_samples, figsize=(4*n_samples, 16))
    
    for i, result in enumerate(all_results):
        # RGB参考图像
        axes[0, i].imshow(np.clip(result['rgb_hr'], 0, 1))
        axes[0, i].set_title(f'样本 {i+1}\nRGB 参考')
        axes[0, i].axis('off')
        
        # 高分辨率真值
        im1 = axes[1, i].imshow(result['hsi_hr'], cmap='viridis', vmin=0, vmax=1)
        axes[1, i].set_title('HR 真值')
        axes[1, i].axis('off')
        
        # 低分辨率输入
        im2 = axes[2, i].imshow(result['hsi_lr'], cmap='viridis', vmin=0, vmax=1)
        axes[2, i].set_title('LR 输入')
        axes[2, i].axis('off')
        
        # 超分辨率输出
        im3 = axes[3, i].imshow(result['output'], cmap='viridis', vmin=0, vmax=1)
        axes[3, i].set_title('SR 输出')
        axes[3, i].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_dir / 'multi_sample_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 多样本对比图保存完成")

def create_error_analysis(save_dir):
    """创建误差分析图"""
    print("生成误差分析图...")
    
    model, device = load_best_model()
    
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    test_cfg = config['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    all_errors = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            
            # 计算误差
            error = torch.abs(output - hsi_hr)
            all_errors.append(error[0, 0].cpu().numpy())  # [273, H, W]
    
    # 计算统计信息
    all_errors = np.array(all_errors)  # [N_samples, 273, H, W]
    
    # 平均误差图
    mean_error = np.mean(all_errors, axis=0)  # [273, H, W]
    std_error = np.std(all_errors, axis=0)
    
    # 选择几个波段显示误差分布
    bands_to_show = [50, 136, 220]
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    for i, band in enumerate(bands_to_show):
        # 平均误差
        im1 = axes[0, i].imshow(mean_error[band], cmap='hot', vmin=0, vmax=0.1)
        axes[0, i].set_title(f'平均误差 - 波段 {band+1}')
        axes[0, i].axis('off')
        plt.colorbar(im1, ax=axes[0, i], fraction=0.046)
        
        # 误差标准差
        im2 = axes[1, i].imshow(std_error[band], cmap='hot', vmin=0, vmax=0.05)
        axes[1, i].set_title(f'误差标准差 - 波段 {band+1}')
        axes[1, i].axis('off')
        plt.colorbar(im2, ax=axes[1, i], fraction=0.046)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'error_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 误差分析图保存完成")

def create_performance_summary(save_dir):
    """创建性能总结图"""
    print("生成性能总结图...")
    
    # 从测试报告中读取数据
    report_path = save_dir / 'test_report.txt'
    if not report_path.exists():
        print("⚠️ 测试报告不存在，跳过性能总结图")
        return
    
    # 手动输入测试结果数据
    models = ['Step-by-Step Best', 'Minimal Training', 'Train-Only Best']
    psnr_values = [31.96, 13.74, 24.53]
    psnr_stds = [5.56, 1.87, 3.30]
    ssim_values = [0.8740, 0.5250, 0.7306]
    ssim_stds = [0.0807, 0.0489, 0.1030]
    loss_values = [0.019897, 0.203682, 0.045574]
    
    # 创建综合性能图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # PSNR柱状图
    bars1 = axes[0, 0].bar(models, psnr_values, yerr=psnr_stds, capsize=5, 
                          color=['#1f77b4', '#ff7f0e', '#2ca02c'], alpha=0.8)
    axes[0, 0].set_title('PSNR 性能对比', fontsize=14, fontweight='bold')
    axes[0, 0].set_ylabel('PSNR (dB)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, mean, std in zip(bars1, psnr_values, psnr_stds):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.5,
                       f'{mean:.2f}±{std:.2f}', ha='center', va='bottom', fontsize=10)
    
    # SSIM柱状图
    bars2 = axes[0, 1].bar(models, ssim_values, yerr=ssim_stds, capsize=5,
                          color=['#1f77b4', '#ff7f0e', '#2ca02c'], alpha=0.8)
    axes[0, 1].set_title('SSIM 性能对比', fontsize=14, fontweight='bold')
    axes[0, 1].set_ylabel('SSIM')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, mean, std in zip(bars2, ssim_values, ssim_stds):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 0.02,
                       f'{mean:.4f}±{std:.4f}', ha='center', va='bottom', fontsize=10)
    
    # Loss对比
    bars3 = axes[1, 0].bar(models, loss_values, 
                          color=['#1f77b4', '#ff7f0e', '#2ca02c'], alpha=0.8)
    axes[1, 0].set_title('Loss 对比', fontsize=14, fontweight='bold')
    axes[1, 0].set_ylabel('L1 Loss')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, loss in zip(bars3, loss_values):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                       f'{loss:.6f}', ha='center', va='bottom', fontsize=10)
    
    # 综合评分雷达图
    categories = ['PSNR\n(归一化)', 'SSIM\n(归一化)', 'Loss\n(反向归一化)']
    
    # 归一化数据 (0-1)
    psnr_norm = [(p - min(psnr_values)) / (max(psnr_values) - min(psnr_values)) for p in psnr_values]
    ssim_norm = [(s - min(ssim_values)) / (max(ssim_values) - min(ssim_values)) for s in ssim_values]
    loss_norm = [1 - (l - min(loss_values)) / (max(loss_values) - min(loss_values)) for l in loss_values]  # 反向
    
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    ax = plt.subplot(2, 2, 4, projection='polar')
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    for i, model in enumerate(models):
        values = [psnr_norm[i], ssim_norm[i], loss_norm[i]]
        values += values[:1]  # 闭合
        ax.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i])
        ax.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    ax.set_title('综合性能雷达图', fontsize=14, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig(save_dir / 'performance_summary.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 性能总结图保存完成")

def main():
    """主函数"""
    print("🎨 生成高级可视化结果")
    print("=" * 50)
    
    save_dir = Path('test_results')
    save_dir.mkdir(exist_ok=True)
    
    try:
        create_spectral_analysis(save_dir)
        create_multi_sample_comparison(save_dir)
        create_error_analysis(save_dir)
        create_performance_summary(save_dir)
        
        print(f"\n🎉 所有高级可视化图表生成完成！")
        print(f"📊 保存位置: {save_dir}")
        print(f"📈 包含图表:")
        print(f"   - spectral_analysis.png: 光谱分析")
        print(f"   - multi_sample_comparison.png: 多样本对比")
        print(f"   - error_analysis.png: 误差分析")
        print(f"   - performance_summary.png: 性能总结")
        
    except Exception as e:
        print(f"❌ 生成可视化时出错: {e}")

if __name__ == '__main__':
    main()
