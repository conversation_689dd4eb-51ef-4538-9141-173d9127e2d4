#!/usr/bin/env python3
"""
完整训练脚本 - DaoGu数据集HSI-RefSR模型训练
"""

import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
import numpy as np

print("🚀 DaoGu数据集HSI-RefSR完整训练开始...")

# 导入模块
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载配置
with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 创建数据集
train_cfg = config['train']['dataset'].copy()
train_cfg.pop('type@', None)
train_dataset = DaoGuSRFDataset(**train_cfg)
print(f"训练数据集: {len(train_dataset)} 个样本")

# 创建数据加载器
train_loader = DataLoader(
    train_dataset,
    batch_size=1,
    shuffle=True,
    num_workers=0,
    pin_memory=True
)

# 创建模型
model = CrossNetHSI(
    use_mask=config['module']['model']['use_mask'],
    use_pwc=config['module']['model']['use_pwc'],
    reweight=config['module']['model']['reweight']
).to(device)

total_params = sum(p.numel() for p in model.parameters())
print(f"模型参数数量: {total_params:,}")

# 创建优化器和损失函数
optimizer = optim.AdamW(
    model.parameters(),
    lr=config['module']['optimizer']['lr'],
    weight_decay=config['module']['optimizer']['weight_decay']
)

criterion = nn.L1Loss()

# 创建保存目录
save_dir = Path('saved/daogu-full-training')
save_dir.mkdir(parents=True, exist_ok=True)

# 训练参数
max_epochs = 30
best_loss = float('inf')
patience = 5  # 早停耐心值
no_improve_count = 0

print(f"\n开始训练 {max_epochs} 个epoch...")
print("=" * 80)

for epoch in range(1, max_epochs + 1):
    print(f"\nEpoch {epoch}/{max_epochs}")
    start_time = time.time()
    
    # 训练
    model.train()
    total_loss = 0.0
    successful_batches = 0
    
    for batch_idx, batch in enumerate(train_loader):
        try:
            # 移动数据到GPU
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            
            # 计算损失
            loss = criterion(output, hsi_hr)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            successful_batches += 1
            
            # 每10个批次打印一次
            if batch_idx % 10 == 0:
                print(f"  Batch {batch_idx+1}/{len(train_loader)}: Loss={loss.item():.6f}")
                
        except Exception as e:
            print(f"  ⚠️ Batch {batch_idx+1} 跳过: {str(e)[:100]}")
            continue
    
    # 计算平均损失
    if successful_batches > 0:
        avg_loss = total_loss / successful_batches
    else:
        avg_loss = float('inf')
    
    epoch_time = time.time() - start_time
    
    print(f"Epoch {epoch} 完成:")
    print(f"  用时: {epoch_time:.1f}s")
    print(f"  成功批次: {successful_batches}/{len(train_loader)}")
    print(f"  平均损失: {avg_loss:.6f}")
    
    # 保存最佳模型
    if avg_loss < best_loss:
        best_loss = avg_loss
        no_improve_count = 0
        
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': avg_loss,
            'best_loss': best_loss,
        }, save_dir / 'best_model.pth')
        
        print(f"  ✅ 保存最佳模型 (Loss: {best_loss:.6f})")
    else:
        no_improve_count += 1
        print(f"  📈 损失未改善 ({no_improve_count}/{patience})")
    
    # 每5个epoch保存检查点
    if epoch % 5 == 0:
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': avg_loss,
        }, save_dir / f'checkpoint_epoch_{epoch}.pth')
        print(f"  💾 保存检查点: epoch_{epoch}")
    
    # 早停检查
    if no_improve_count >= patience:
        print(f"\n⏹️ 早停触发！连续 {patience} 个epoch无改善")
        break
    
    print("-" * 80)

# 保存最终模型
torch.save({
    'epoch': epoch,
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'train_loss': avg_loss,
    'best_loss': best_loss,
}, save_dir / 'final_model.pth')

print(f"\n🎉 训练完成！")
print(f"最佳损失: {best_loss:.6f}")
print(f"训练了 {epoch} 个epoch")
print(f"模型保存在: {save_dir}")

# 训练总结
print("\n" + "=" * 80)
print("🎯 DaoGu数据集HSI-RefSR模型训练总结")
print("=" * 80)
print(f"✅ 数据集: DaoGu SWIR (273波段, 996-2501nm)")
print(f"✅ 样本数量: {len(train_dataset)} 个训练样本")
print(f"✅ 模型: CrossNetHSI (参数数量: {total_params:,})")
print(f"✅ 最佳训练损失: {best_loss:.6f}")
print(f"✅ 训练epoch数: {epoch}")
print(f"✅ 模型保存路径: {save_dir}")
print("\n🚀 训练成功完成！模型已准备好用于推理和测试")
