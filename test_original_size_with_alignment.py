#!/usr/bin/env python3
"""
原尺寸图像测试脚本 - 包含对齐模块测试
测试HSI-RefSR模型在原始尺寸图像上的性能，并展示对齐效果
"""

import time
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import cv2
from scipy.io import loadmat

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模块
from hsirsr.model.refsr import CrossNetHSI

# 复制必要的函数
def minmax_normalize(img):
    """Min-max normalization"""
    return (img - img.min()) / (img.max() - img.min() + 1e-8)

def hwc2chw(img):
    """Convert HWC to CHW format"""
    return img.transpose(2, 0, 1)

def hsi_hwc2chw(img):
    """Convert HSI HWC to CHW format with additional dimension"""
    return img.transpose(2, 0, 1)[None, ...]  # [1, C, H, W]

class SRDegrade:
    """Simple degradation for super-resolution"""
    def __init__(self, scale_factor):
        self.scale_factor = scale_factor

    def __call__(self, img):
        h, w = img.shape[:2]
        new_h, new_w = h // self.scale_factor, w // self.scale_factor
        if len(img.shape) == 3:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        else:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)

def create_swir_srf(num_bands):
    """创建适配SWIR数据的光谱响应函数"""
    srf = np.zeros((3, num_bands), dtype=np.float32)
    
    # 为SWIR波段创建简化的RGB映射
    band_per_channel = num_bands // 3
    
    # R通道 - 对应较长波长
    srf[0, :band_per_channel] = np.linspace(0.8, 0.2, band_per_channel)
    
    # G通道 - 对应中等波长  
    start_g = band_per_channel
    end_g = 2 * band_per_channel
    srf[1, start_g:end_g] = np.linspace(0.2, 0.8, band_per_channel)
    
    # B通道 - 对应较短波长
    start_b = 2 * band_per_channel
    srf[2, start_b:] = np.linspace(0.8, 0.2, num_bands - start_b)
    
    return srf

class OriginalSizeDataset:
    """原尺寸数据集类"""
    
    def __init__(self, root, input, ref, names_path, sf):
        root = Path(root)
        self.hsi_inp_dir = root / (input+'_hsi') / 'HR'
        self.hsi_rgb_dir = root / input / 'HR'
        self.rgb_dir = root / ref / 'HR'
        
        self.sf = sf
        
        # 读取样本名称
        with open(root / names_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        self.names = names
        
        # 创建适配SWIR数据的SRF
        self.srf = create_swir_srf(273)
        
        # 设置降采样
        self.degrade = SRDegrade(sf)
        
        print(f"原尺寸数据集初始化完成:")
        print(f"  样本数量: {len(self.names)}")
        print(f"  SRF形状: {self.srf.shape}")
        print(f"  缩放因子: {sf}")

    def get_mat(self, path):
        """加载.mat文件"""
        return loadmat(path)

    def get_png(self, path):
        """加载.png文件"""
        import imageio
        img = imageio.imread(path)
        img = np.array(img).astype('float')
        img = img / 255.0
        return img

    def __len__(self):
        return len(self.names)

    def __getitem__(self, index):
        name = self.names[index]

        # 加载HSI数据 (MAT格式) - 原始尺寸 447x384x273
        hsi_hr = self.get_mat(str(self.hsi_inp_dir / (name+'.mat')))['gt']
        hsi_hr = minmax_normalize(hsi_hr.astype('float'))

        print(f"原始HSI尺寸: {hsi_hr.shape}")

        # 加载RGB参考数据 (PNG格式) - 偏移后的ground truth，原始尺寸 447x384x3
        rgb_offset_hr = self.get_png(str(self.rgb_dir / (name+'.png')))
        rgb_offset_hr = minmax_normalize(rgb_offset_hr.astype('float'))

        # 加载HSI生成的伪RGB (PNG格式) - 下采样的，尺寸约 111x96x3
        hsi_pseudo_rgb_lr = self.get_png(str(self.hsi_rgb_dir / (name+'.png')))
        hsi_pseudo_rgb_lr = minmax_normalize(hsi_pseudo_rgb_lr.astype('float'))

        print(f"偏移RGB尺寸: {rgb_offset_hr.shape}")
        print(f"伪RGB LR尺寸: {hsi_pseudo_rgb_lr.shape}")

        # 使用SRF将HSI转换为RGB (生成高分辨率的伪RGB)
        hsi_rgb_hr = hsi_hr @ self.srf.T
        print(f"HSI生成RGB尺寸: {hsi_rgb_hr.shape}")

        # 确保HSI和RGB尺寸一致 (都使用原始高分辨率)
        h, w = hsi_hr.shape[:2]  # 447, 384

        # 调整RGB图像到HSI尺寸 (如果需要)
        if rgb_offset_hr.shape[:2] != (h, w):
            rgb_offset_hr = cv2.resize(rgb_offset_hr, (w, h), interpolation=cv2.INTER_LINEAR)
        if hsi_rgb_hr.shape[:2] != (h, w):
            hsi_rgb_hr = cv2.resize(hsi_rgb_hr, (w, h), interpolation=cv2.INTER_LINEAR)

        # 将伪RGB LR上采样到高分辨率用于对比
        hsi_pseudo_rgb_hr = cv2.resize(hsi_pseudo_rgb_lr, (w, h), interpolation=cv2.INTER_LINEAR)

        # 生成低分辨率版本 (用于模型输入)
        hsi_lr = self.degrade(hsi_hr)
        hsi_rgb_lr = self.degrade(hsi_rgb_hr)
        rgb_offset_lr = self.degrade(rgb_offset_hr)

        print(f"最终尺寸 - HSI_HR: {hsi_hr.shape}, HSI_LR: {hsi_lr.shape}")
        print(f"RGB_HR: {rgb_offset_hr.shape}, RGB_LR: {rgb_offset_lr.shape}")

        # 处理数据格式
        hsi_hr_processed = hsi_hwc2chw(hsi_hr).astype(np.float32)
        hsi_lr_processed = hsi_hwc2chw(hsi_lr).astype(np.float32)

        hsi_rgb_hr_processed = hwc2chw(hsi_rgb_hr).astype(np.float32)
        hsi_rgb_lr_processed = hwc2chw(hsi_rgb_lr).astype(np.float32)
        rgb_offset_hr_processed = hwc2chw(rgb_offset_hr).astype(np.float32)
        rgb_offset_lr_processed = hwc2chw(rgb_offset_lr).astype(np.float32)
        hsi_pseudo_rgb_hr_processed = hwc2chw(hsi_pseudo_rgb_hr).astype(np.float32)
        hsi_pseudo_rgb_lr_processed = hwc2chw(hsi_pseudo_rgb_lr).astype(np.float32)

        return {
            'name': name,
            'hsi_hr': hsi_hr_processed,                    # [1, 273, 447, 384] - 目标
            'hsi_lr': hsi_lr_processed,                    # [1, 273, 111, 96] - 输入
            'hsi_rgb_hr': hsi_rgb_hr_processed,            # [3, 447, 384] - HSI生成的高分RGB
            'hsi_rgb_lr': hsi_rgb_lr_processed,            # [3, 111, 96] - HSI生成的低分RGB (模型输入)
            'rgb_offset_hr': rgb_offset_hr_processed,      # [3, 447, 384] - 偏移后的ground truth (模型输入)
            'rgb_offset_lr': rgb_offset_lr_processed,      # [3, 111, 96] - 偏移后的低分RGB
            'hsi_pseudo_rgb_hr': hsi_pseudo_rgb_hr_processed,  # [3, 447, 384] - 上采样的伪RGB
            'hsi_pseudo_rgb_lr': hsi_pseudo_rgb_lr_processed,  # [3, 111, 96] - 原始伪RGB LR
            'original_shape': (h, w)
        }

def load_best_model():
    """加载最佳模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
    
    checkpoint = torch.load('saved/step-by-step-training/best_model.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, device

def calculate_alignment_error(img1, img2):
    """计算两张图像的对齐误差"""
    if isinstance(img1, torch.Tensor):
        img1 = img1.detach().cpu().numpy()
    if isinstance(img2, torch.Tensor):
        img2 = img2.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    img1 = np.clip(img1, 0, 1)
    img2 = np.clip(img2, 0, 1)
    
    # 计算像素级差异
    diff = np.abs(img1 - img2)
    mean_error = np.mean(diff)
    max_error = np.max(diff)
    
    return diff, mean_error, max_error

def create_alignment_visualization(sample_data, model_output, save_dir, sample_idx):
    """创建对齐效果可视化"""

    name = sample_data['name']
    hsi_rgb_hr = sample_data['hsi_rgb_hr']          # [3, H, W] HSI生成的高分RGB
    rgb_offset_hr = sample_data['rgb_offset_hr']    # [3, H, W] 偏移后的ground truth
    hsi_pseudo_rgb_lr = sample_data['hsi_pseudo_rgb_lr']  # [3, h, w] 原始伪RGB LR

    # 模型输出
    hsi_sr_output = model_output['hsi_sr']          # [273, H, W]
    ref_warp = model_output['ref_warp']             # [3, H, W] 对齐后的RGB
    flow = model_output['flow']                     # [2, H, W] 光流

    # 转换为numpy并调整维度
    hsi_rgb_hr_np = hsi_rgb_hr.cpu().numpy().transpose(1, 2, 0)         # [H, W, 3]
    rgb_offset_hr_np = rgb_offset_hr.cpu().numpy().transpose(1, 2, 0)   # [H, W, 3]
    ref_warp_np = ref_warp.cpu().numpy().transpose(1, 2, 0)             # [H, W, 3]
    hsi_pseudo_rgb_lr_np = hsi_pseudo_rgb_lr.cpu().numpy().transpose(1, 2, 0)  # [h, w, 3]
    
    # 计算对齐前后的差异 (使用HSI生成的高分RGB作为参考)
    diff_before, mean_before, max_before = calculate_alignment_error(hsi_rgb_hr_np, rgb_offset_hr_np)
    diff_after, mean_after, max_after = calculate_alignment_error(hsi_rgb_hr_np, ref_warp_np)

    # 创建可视化
    fig, axes = plt.subplots(2, 5, figsize=(25, 10))
    fig.suptitle(f'Sample {sample_idx+1} ({name}) - Original Size Alignment Analysis\n'
                 f'Size: {hsi_rgb_hr_np.shape[0]}×{hsi_rgb_hr_np.shape[1]} | '
                 f'Before Error: {mean_before:.4f} | After Error: {mean_after:.4f}',
                 fontsize=16, fontweight='bold')

    # 第一行：输入图像和对齐结果
    # 1. HSI生成的伪RGB (低分辨率)
    axes[0, 0].imshow(np.clip(hsi_pseudo_rgb_lr_np, 0, 1))
    axes[0, 0].set_title(f'HSI Pseudo RGB (LR)\n{hsi_pseudo_rgb_lr_np.shape[0]}×{hsi_pseudo_rgb_lr_np.shape[1]}')
    axes[0, 0].axis('off')

    # 2. HSI生成的RGB (高分辨率)
    axes[0, 1].imshow(np.clip(hsi_rgb_hr_np, 0, 1))
    axes[0, 1].set_title(f'HSI Generated RGB (HR)\n{hsi_rgb_hr_np.shape[0]}×{hsi_rgb_hr_np.shape[1]}')
    axes[0, 1].axis('off')
    
    # 3. 偏移后的Ground Truth RGB
    axes[0, 2].imshow(np.clip(rgb_offset_hr_np, 0, 1))
    axes[0, 2].set_title(f'Offset Ground Truth RGB\n{rgb_offset_hr_np.shape[0]}×{rgb_offset_hr_np.shape[1]} (Before Alignment)')
    axes[0, 2].axis('off')

    # 4. 对齐后的RGB
    axes[0, 3].imshow(np.clip(ref_warp_np, 0, 1))
    axes[0, 3].set_title(f'Aligned RGB\n{ref_warp_np.shape[0]}×{ref_warp_np.shape[1]} (After Alignment)')
    axes[0, 3].axis('off')

    # 5. 光流可视化
    flow_np = flow.cpu().numpy()  # [2, H, W]
    flow_magnitude = np.sqrt(flow_np[0]**2 + flow_np[1]**2)
    im_flow = axes[0, 4].imshow(flow_magnitude, cmap='jet')
    axes[0, 4].set_title('Optical Flow Magnitude')
    axes[0, 4].axis('off')
    plt.colorbar(im_flow, ax=axes[0, 4], fraction=0.046)
    
    # 第二行：差异分析
    # 1. 对齐前差异
    im1 = axes[1, 0].imshow(diff_before, cmap='hot', vmin=0, vmax=0.3)
    axes[1, 0].set_title(f'Difference Before Alignment\nMean: {mean_before:.4f}, Max: {max_before:.4f}')
    axes[1, 0].axis('off')
    plt.colorbar(im1, ax=axes[1, 0], fraction=0.046)

    # 2. 对齐后差异
    im2 = axes[1, 1].imshow(diff_after, cmap='hot', vmin=0, vmax=0.3)
    axes[1, 1].set_title(f'Difference After Alignment\nMean: {mean_after:.4f}, Max: {max_after:.4f}')
    axes[1, 1].axis('off')
    plt.colorbar(im2, ax=axes[1, 1], fraction=0.046)

    # 3. 超分辨率结果 (选择中间波段)
    mid_band = hsi_sr_output.shape[0] // 2  # hsi_sr_output is [273, H, W]
    hsi_sr_band = hsi_sr_output[mid_band].cpu().numpy()
    im3 = axes[1, 2].imshow(hsi_sr_band, cmap='viridis', vmin=0, vmax=1)
    axes[1, 2].set_title(f'HSI Super-Resolution\nBand {mid_band+1}/273')
    axes[1, 2].axis('off')
    plt.colorbar(im3, ax=axes[1, 2], fraction=0.046)

    # 4. HSI RGB对比 (真实 vs 超分)
    # 使用SRF将超分HSI转换为RGB进行对比
    srf = create_swir_srf(273)
    hsi_sr_np = hsi_sr_output.cpu().numpy().transpose(1, 2, 0)  # [H, W, 273]
    hsi_sr_rgb = hsi_sr_np @ srf.T  # [H, W, 3]
    axes[1, 3].imshow(np.clip(hsi_sr_rgb, 0, 1))
    axes[1, 3].set_title('HSI SR → RGB\n(Super-resolved HSI as RGB)')
    axes[1, 3].axis('off')

    # 5. 改善程度统计
    axes[1, 4].axis('off')
    improvement_text = f"""
Alignment Improvement:
• Mean Error Reduction: {((mean_before - mean_after) / mean_before * 100):.1f}%
• Max Error Reduction: {((max_before - max_after) / max_before * 100):.1f}%

Flow Statistics:
• Mean Flow Magnitude: {np.mean(flow_magnitude):.3f}
• Max Flow Magnitude: {np.max(flow_magnitude):.3f}

Original Image Size: {hsi_rgb_hr_np.shape[0]}×{hsi_rgb_hr_np.shape[1]}
HSI Bands: 273
Scale Factor: 4x
Processing: Full Resolution
    """

    axes[1, 4].text(0.05, 0.95, improvement_text, transform=axes[1, 4].transAxes,
                    fontsize=11, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_dir / f'sample_{sample_idx+1}_alignment_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return {
        'mean_error_before': mean_before,
        'mean_error_after': mean_after,
        'max_error_before': max_before,
        'max_error_after': max_after,
        'improvement_percent': (mean_before - mean_after) / mean_before * 100,
        'flow_magnitude_mean': np.mean(flow_magnitude),
        'flow_magnitude_max': np.max(flow_magnitude)
    }

def test_original_size_with_alignment():
    """测试原尺寸图像和对齐模块"""
    print("🔍 开始原尺寸图像和对齐模块测试")
    print("=" * 80)
    
    # 加载模型
    model, device = load_best_model()
    print(f"✅ 模型加载完成，使用设备: {device}")
    
    # 创建原尺寸数据集
    dataset = OriginalSizeDataset(
        root='data/daogu_processed',
        input='img1',
        ref='img2', 
        names_path='test.txt',
        sf=4
    )
    
    print(f"📊 测试数据集: {len(dataset)} 个样本")
    
    # 创建保存目录
    save_dir = Path('original_size_alignment_results')
    save_dir.mkdir(exist_ok=True)
    
    # 存储所有结果
    all_results = []
    criterion = nn.L1Loss()
    
    print("\n🚀 开始逐一测试所有样本...")
    
    with torch.no_grad():
        for sample_idx in range(len(dataset)):
            print(f"\n--- 测试样本 {sample_idx+1}/{len(dataset)} ---")
            
            try:
                # 获取样本数据
                sample_data = dataset[sample_idx]
                name = sample_data['name']
                
                print(f"  样本名称: {name}")
                print(f"  原始尺寸: {sample_data['original_shape']}")
                
                # 移动数据到GPU
                hsi_hr = torch.from_numpy(sample_data['hsi_hr']).unsqueeze(0).to(device)
                hsi_lr = torch.from_numpy(sample_data['hsi_lr']).unsqueeze(0).to(device)
                hsi_rgb_lr = torch.from_numpy(sample_data['hsi_rgb_lr']).unsqueeze(0).to(device)
                rgb_offset_hr = torch.from_numpy(sample_data['rgb_offset_hr']).unsqueeze(0).to(device)
                hsi_rgb_hr = torch.from_numpy(sample_data['hsi_rgb_hr']).unsqueeze(0).to(device)
                hsi_pseudo_rgb_lr = torch.from_numpy(sample_data['hsi_pseudo_rgb_lr']).unsqueeze(0).to(device)

                print(f"  数据形状: HSI_HR={hsi_hr.shape}, HSI_LR={hsi_lr.shape}, RGB_HR={rgb_offset_hr.shape}")

                # 前向传播 - 测试对齐模块
                start_time = time.time()
                hsi_sr_output, ref_warp, flow, masks = model(
                    hsi_sr=hsi_lr,
                    hsi_rgb_sr=hsi_rgb_lr,
                    ref_hr=rgb_offset_hr
                )
                inference_time = time.time() - start_time

                # 计算损失
                loss = criterion(hsi_sr_output, hsi_hr)

                print(f"  ✅ 推理完成，Loss: {loss.item():.6f}")
                print(f"  ⏱️ 推理时间: {inference_time:.3f}s")
                print(f"  📐 输出尺寸: {hsi_sr_output.shape}")

                # 准备可视化数据
                model_output = {
                    'hsi_sr': hsi_sr_output[0],      # [1, 273, H, W] -> [273, H, W]
                    'ref_warp': ref_warp[0],         # [1, 3, H, W] -> [3, H, W]
                    'flow': flow[0],                 # [1, 2, H, W] -> [2, H, W]
                    'masks': masks[0] if masks is not None else None
                }

                # 生成对齐效果可视化
                print(f"  📊 生成对齐效果分析...")
                alignment_stats = create_alignment_visualization(
                    {
                        'name': name,
                        'hsi_rgb_hr': hsi_rgb_hr[0],           # HSI生成的高分RGB
                        'rgb_offset_hr': rgb_offset_hr[0],     # 偏移后的ground truth
                        'hsi_pseudo_rgb_lr': hsi_pseudo_rgb_lr[0]  # 原始伪RGB LR
                    },
                    model_output,
                    save_dir,
                    sample_idx
                )
                
                # 保存结果
                result = {
                    'sample_idx': sample_idx,
                    'name': name,
                    'original_shape': sample_data['original_shape'],
                    'loss': loss.item(),
                    'inference_time': inference_time,
                    'alignment_stats': alignment_stats
                }
                
                all_results.append(result)
                
                print(f"  📈 对齐改善: {alignment_stats['improvement_percent']:.1f}%")
                
                # 清理GPU缓存
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"  ❌ 样本 {sample_idx+1} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                continue
    
    # 生成总结报告
    create_alignment_summary_report(all_results, save_dir)
    
    print(f"\n🎉 原尺寸对齐测试完成！")
    print(f"📁 结果保存在: {save_dir}")
    print(f"📊 共测试 {len(all_results)} 个样本")
    
    return all_results

def create_alignment_summary_report(all_results, save_dir):
    """创建对齐测试总结报告"""
    print("\n📝 生成对齐测试总结报告...")
    
    # 计算统计信息
    alignment_improvements = [r['alignment_stats']['improvement_percent'] for r in all_results]
    mean_errors_before = [r['alignment_stats']['mean_error_before'] for r in all_results]
    mean_errors_after = [r['alignment_stats']['mean_error_after'] for r in all_results]
    flow_magnitudes = [r['alignment_stats']['flow_magnitude_mean'] for r in all_results]
    inference_times = [r['inference_time'] for r in all_results]
    
    # 写入文本报告
    with open(save_dir / 'alignment_test_report.txt', 'w', encoding='utf-8') as f:
        f.write("DaoGu数据集原尺寸对齐测试报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试样本数: {len(all_results)}\n")
        f.write(f"模型: Step-by-Step Best (包含对齐模块)\n\n")
        
        f.write("对齐效果统计:\n")
        f.write(f"  平均对齐改善: {np.mean(alignment_improvements):.2f}% ± {np.std(alignment_improvements):.2f}%\n")
        f.write(f"  对齐前平均误差: {np.mean(mean_errors_before):.6f} ± {np.std(mean_errors_before):.6f}\n")
        f.write(f"  对齐后平均误差: {np.mean(mean_errors_after):.6f} ± {np.std(mean_errors_after):.6f}\n")
        f.write(f"  平均光流幅度: {np.mean(flow_magnitudes):.4f} ± {np.std(flow_magnitudes):.4f}\n")
        f.write(f"  平均推理时间: {np.mean(inference_times):.3f} ± {np.std(inference_times):.3f} s\n\n")
        
        f.write("各样本详细结果:\n")
        f.write("样本名称           原始尺寸    对齐改善(%)  推理时间(s)  光流幅度\n")
        f.write("-" * 70 + "\n")
        
        for result in all_results:
            f.write(f"{result['name']:15s}  {str(result['original_shape']):12s}  "
                   f"{result['alignment_stats']['improvement_percent']:10.1f}  "
                   f"{result['inference_time']:10.3f}  "
                   f"{result['alignment_stats']['flow_magnitude_mean']:8.4f}\n")
    
    print("✅ 对齐测试总结报告保存完成")

def main():
    """主函数"""
    all_results = test_original_size_with_alignment()
    
    if all_results:
        alignment_improvements = [r['alignment_stats']['improvement_percent'] for r in all_results]
        print(f"\n📈 对齐测试统计:")
        print(f"   平均对齐改善: {np.mean(alignment_improvements):.2f}% ± {np.std(alignment_improvements):.2f}%")
        print(f"   最佳对齐改善: {np.max(alignment_improvements):.2f}% (样本{np.argmax(alignment_improvements)+1})")
        print(f"   最差对齐改善: {np.min(alignment_improvements):.2f}% (样本{np.argmin(alignment_improvements)+1})")

if __name__ == '__main__':
    main()
