#!/usr/bin/env python3
"""
测试集中所有图像的完整测试脚本
对每个样本进行详细分析和可视化
"""

import os
import time
import yaml
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from torch.utils.data import DataLoader
from skimage.metrics import peak_signal_noise_ratio, structural_similarity
import cv2

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模块
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

def calculate_metrics(pred, target):
    """计算PSNR和SSIM指标"""
    if isinstance(pred, torch.Tensor):
        pred = pred.detach().cpu().numpy()
    if isinstance(target, torch.Tensor):
        target = target.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    pred = np.clip(pred, 0, 1)
    target = np.clip(target, 0, 1)
    
    # 计算PSNR
    psnr = peak_signal_noise_ratio(target, pred, data_range=1.0)
    
    # 计算SSIM
    ssim = structural_similarity(target, pred, data_range=1.0)
    
    return psnr, ssim

def load_best_model():
    """加载最佳模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
    
    checkpoint = torch.load('saved/step-by-step-training/best_model.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, device

def create_sample_detailed_analysis(sample_data, sample_idx, save_dir):
    """创建单个样本的详细分析"""
    
    hsi_hr = sample_data['hsi_hr']  # [273, H, W]
    hsi_lr = sample_data['hsi_lr']
    output = sample_data['output']
    rgb_hr = sample_data['rgb_hr']  # [3, H, W]
    psnr = sample_data['psnr']
    ssim = sample_data['ssim']
    loss = sample_data['loss']
    
    # 创建大图显示
    fig = plt.figure(figsize=(20, 16))
    fig.suptitle(f'Sample {sample_idx+1} - Detailed Analysis\nPSNR: {psnr:.2f}dB, SSIM: {ssim:.4f}, Loss: {loss:.6f}', 
                 fontsize=16, fontweight='bold')
    
    # 选择多个波段进行可视化
    bands_to_show = [30, 80, 136, 180, 220, 260]  # 6个不同波长区域
    wavelengths = np.linspace(996, 2501, 273)
    
    # 1. RGB参考图像
    plt.subplot(4, 6, 1)
    if rgb_hr.shape[0] == 3:  # [3, H, W]
        rgb_display = np.transpose(rgb_hr, (1, 2, 0))
    else:
        rgb_display = rgb_hr
    plt.imshow(np.clip(rgb_display, 0, 1))
    plt.title('RGB Reference')
    plt.axis('off')
    
    # 2-7. 不同波段的HR真值
    for i, band in enumerate(bands_to_show):
        plt.subplot(4, 6, i+2)
        im = plt.imshow(hsi_hr[band], cmap='viridis', vmin=0, vmax=1)
        plt.title(f'HR Band {band+1}\n({wavelengths[band]:.0f}nm)')
        plt.axis('off')
        plt.colorbar(im, fraction=0.046)
    
    # 8-13. 对应波段的LR输入
    for i, band in enumerate(bands_to_show):
        plt.subplot(4, 6, i+8)
        im = plt.imshow(hsi_lr[band], cmap='viridis', vmin=0, vmax=1)
        plt.title(f'LR Band {band+1}')
        plt.axis('off')
        plt.colorbar(im, fraction=0.046)
    
    # 14-19. 对应波段的SR输出
    for i, band in enumerate(bands_to_show):
        plt.subplot(4, 6, i+14)
        im = plt.imshow(output[band], cmap='viridis', vmin=0, vmax=1)
        plt.title(f'SR Band {band+1}')
        plt.axis('off')
        plt.colorbar(im, fraction=0.046)
    
    # 20-24. 对应波段的误差图
    for i, band in enumerate(bands_to_show[:5]):
        plt.subplot(4, 6, i+20)
        error = np.abs(hsi_hr[band] - output[band])
        im = plt.imshow(error, cmap='hot', vmin=0, vmax=0.2)
        plt.title(f'Error Band {band+1}')
        plt.axis('off')
        plt.colorbar(im, fraction=0.046)
    
    # 24. 光谱曲线对比
    plt.subplot(4, 6, 24)
    h, w = hsi_hr.shape[1], hsi_hr.shape[2]
    center_y, center_x = h // 2, w // 2
    
    spectrum_hr = hsi_hr[:, center_y, center_x]
    spectrum_lr = hsi_lr[:, center_y, center_x]
    spectrum_sr = output[:, center_y, center_x]
    
    plt.plot(wavelengths, spectrum_hr, 'b-', label='HR Truth', linewidth=2)
    plt.plot(wavelengths, spectrum_sr, 'r--', label='SR Output', linewidth=2)
    plt.plot(wavelengths, spectrum_lr, 'g:', label='LR Input', linewidth=1, alpha=0.7)
    plt.xlabel('Wavelength (nm)')
    plt.ylabel('Reflectance')
    plt.title('Center Pixel Spectrum')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_dir / f'sample_{sample_idx+1}_complete_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_spectral_comparison(sample_data, sample_idx, save_dir):
    """创建光谱对比分析"""
    
    hsi_hr = sample_data['hsi_hr']
    hsi_lr = sample_data['hsi_lr']
    output = sample_data['output']
    
    wavelengths = np.linspace(996, 2501, 273)
    h, w = hsi_hr.shape[1], hsi_hr.shape[2]
    
    # 选择多个像素点进行光谱分析
    positions = [
        (h//4, w//4),      # 左上
        (h//4, 3*w//4),    # 右上
        (h//2, w//2),      # 中心
        (3*h//4, w//4),    # 左下
        (3*h//4, 3*w//4),  # 右下
    ]
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Sample {sample_idx+1} - Spectral Analysis at Different Positions', 
                 fontsize=16, fontweight='bold')
    
    for i, (y, x) in enumerate(positions):
        ax = axes[i//3, i%3]
        
        spectrum_hr = hsi_hr[:, y, x]
        spectrum_lr = hsi_lr[:, y, x]
        spectrum_sr = output[:, y, x]
        
        ax.plot(wavelengths, spectrum_hr, 'b-', label='HR Truth', linewidth=2)
        ax.plot(wavelengths, spectrum_sr, 'r--', label='SR Output', linewidth=2)
        ax.plot(wavelengths, spectrum_lr, 'g:', label='LR Input', linewidth=1, alpha=0.7)
        
        ax.set_xlabel('Wavelength (nm)')
        ax.set_ylabel('Reflectance')
        ax.set_title(f'Position ({y}, {x})')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 计算相关系数
        correlation = np.corrcoef(spectrum_hr, spectrum_sr)[0, 1]
        ax.text(0.05, 0.95, f'Corr: {correlation:.4f}', 
                transform=ax.transAxes, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # 最后一个子图显示整体统计
    ax = axes[1, 2]
    
    # 计算每个波段的平均误差
    band_errors = np.mean(np.abs(hsi_hr - output), axis=(1, 2))
    ax.plot(wavelengths, band_errors, 'purple', linewidth=2)
    ax.set_xlabel('Wavelength (nm)')
    ax.set_ylabel('Mean Absolute Error')
    ax.set_title('Band-wise Reconstruction Error')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_dir / f'sample_{sample_idx+1}_spectral_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def test_all_samples():
    """测试所有样本"""
    print("🔍 开始测试集中所有图像的完整测试")
    print("=" * 80)
    
    # 加载模型
    model, device = load_best_model()
    print(f"✅ 模型加载完成，使用设备: {device}")
    
    # 加载测试数据
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    test_cfg = config['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    
    print(f"📊 测试数据集: {len(test_dataset)} 个样本")
    
    # 创建保存目录
    save_dir = Path('all_samples_results')
    save_dir.mkdir(exist_ok=True)
    
    # 存储所有结果
    all_results = []
    criterion = nn.L1Loss()
    
    print("\n🚀 开始逐一测试所有样本...")
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            print(f"\n--- 测试样本 {batch_idx+1}/{len(test_dataset)} ---")
            
            try:
                # 移动数据到GPU
                hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
                hsi_hr = hsi_hr.to(device)
                hsi_lr = hsi_lr.to(device)
                hsi_rgb_lr = hsi_rgb_lr.to(device)
                rgb_hr = rgb_hr.to(device)
                
                print(f"  数据形状: HSI_HR={hsi_hr.shape}, HSI_LR={hsi_lr.shape}, RGB_HR={rgb_hr.shape}")
                
                # 前向传播
                start_time = time.time()
                output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
                inference_time = time.time() - start_time
                
                # 计算损失
                loss = criterion(output, hsi_hr)
                
                # 转换为numpy进行指标计算
                hsi_hr_np = hsi_hr[0, 0].cpu().numpy()  # [273, H, W]
                hsi_lr_np = hsi_lr[0, 0].cpu().numpy()
                output_np = output[0, 0].cpu().numpy()
                rgb_hr_np = rgb_hr[0].cpu().numpy()  # [3, H, W]
                
                # 计算多个波段的平均指标
                psnr_values = []
                ssim_values = []
                
                # 选择代表性波段计算指标
                test_bands = [50, 100, 136, 180, 220]
                for band in test_bands:
                    if band < hsi_hr_np.shape[0]:
                        psnr, ssim = calculate_metrics(output_np[band], hsi_hr_np[band])
                        psnr_values.append(psnr)
                        ssim_values.append(ssim)
                
                avg_psnr = np.mean(psnr_values)
                avg_ssim = np.mean(ssim_values)
                
                # 保存样本数据
                sample_data = {
                    'sample_idx': batch_idx,
                    'hsi_hr': hsi_hr_np,
                    'hsi_lr': hsi_lr_np,
                    'output': output_np,
                    'rgb_hr': rgb_hr_np,
                    'psnr': avg_psnr,
                    'ssim': avg_ssim,
                    'loss': loss.item(),
                    'inference_time': inference_time,
                    'psnr_per_band': psnr_values,
                    'ssim_per_band': ssim_values
                }
                
                all_results.append(sample_data)
                
                print(f"  ✅ PSNR: {avg_psnr:.2f}dB, SSIM: {avg_ssim:.4f}, Loss: {loss.item():.6f}")
                print(f"  ⏱️ 推理时间: {inference_time:.3f}s")
                
                # 生成详细分析图
                print(f"  📊 生成详细分析图...")
                create_sample_detailed_analysis(sample_data, batch_idx, save_dir)
                create_spectral_comparison(sample_data, batch_idx, save_dir)
                
                # 清理GPU缓存
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"  ❌ 样本 {batch_idx+1} 测试失败: {e}")
                continue
    
    # 生成总结报告
    create_summary_report(all_results, save_dir)
    create_all_samples_comparison(all_results, save_dir)
    
    print(f"\n🎉 所有样本测试完成！")
    print(f"📁 结果保存在: {save_dir}")
    print(f"📊 共测试 {len(all_results)} 个样本")
    
    return all_results

def create_summary_report(all_results, save_dir):
    """创建总结报告"""
    print("\n📝 生成总结报告...")
    
    # 计算统计信息
    psnr_values = [r['psnr'] for r in all_results]
    ssim_values = [r['ssim'] for r in all_results]
    loss_values = [r['loss'] for r in all_results]
    inference_times = [r['inference_time'] for r in all_results]
    
    # 写入文本报告
    with open(save_dir / 'all_samples_report.txt', 'w', encoding='utf-8') as f:
        f.write("DaoGu数据集所有样本测试报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试样本数: {len(all_results)}\n")
        f.write(f"模型: Step-by-Step Best\n\n")
        
        f.write("整体统计结果:\n")
        f.write(f"  平均PSNR: {np.mean(psnr_values):.2f} ± {np.std(psnr_values):.2f} dB\n")
        f.write(f"  平均SSIM: {np.mean(ssim_values):.4f} ± {np.std(ssim_values):.4f}\n")
        f.write(f"  平均Loss: {np.mean(loss_values):.6f} ± {np.std(loss_values):.6f}\n")
        f.write(f"  平均推理时间: {np.mean(inference_times):.3f} ± {np.std(inference_times):.3f} s\n\n")
        
        f.write("各样本详细结果:\n")
        f.write("样本ID  PSNR(dB)   SSIM      Loss      推理时间(s)\n")
        f.write("-" * 55 + "\n")
        
        for i, result in enumerate(all_results):
            f.write(f"{i+1:6d}  {result['psnr']:8.2f}  {result['ssim']:8.4f}  "
                   f"{result['loss']:8.6f}  {result['inference_time']:10.3f}\n")
        
        # 最佳和最差样本
        best_idx = np.argmax(psnr_values)
        worst_idx = np.argmin(psnr_values)
        
        f.write(f"\n最佳样本: 样本{best_idx+1} (PSNR: {psnr_values[best_idx]:.2f}dB)\n")
        f.write(f"最差样本: 样本{worst_idx+1} (PSNR: {psnr_values[worst_idx]:.2f}dB)\n")
    
    print("✅ 总结报告保存完成")

def create_all_samples_comparison(all_results, save_dir):
    """创建所有样本的对比图"""
    print("📊 生成所有样本对比图...")
    
    n_samples = len(all_results)
    psnr_values = [r['psnr'] for r in all_results]
    ssim_values = [r['ssim'] for r in all_results]
    loss_values = [r['loss'] for r in all_results]
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('All Samples Performance Comparison', fontsize=16, fontweight='bold')
    
    # PSNR对比
    axes[0, 0].bar(range(1, n_samples+1), psnr_values, color='skyblue', alpha=0.8)
    axes[0, 0].set_title('PSNR Comparison Across All Samples')
    axes[0, 0].set_xlabel('Sample ID')
    axes[0, 0].set_ylabel('PSNR (dB)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加平均线
    mean_psnr = np.mean(psnr_values)
    axes[0, 0].axhline(y=mean_psnr, color='red', linestyle='--', 
                       label=f'Mean: {mean_psnr:.2f}dB')
    axes[0, 0].legend()
    
    # SSIM对比
    axes[0, 1].bar(range(1, n_samples+1), ssim_values, color='orange', alpha=0.8)
    axes[0, 1].set_title('SSIM Comparison Across All Samples')
    axes[0, 1].set_xlabel('Sample ID')
    axes[0, 1].set_ylabel('SSIM')
    axes[0, 1].grid(True, alpha=0.3)
    
    mean_ssim = np.mean(ssim_values)
    axes[0, 1].axhline(y=mean_ssim, color='red', linestyle='--',
                       label=f'Mean: {mean_ssim:.4f}')
    axes[0, 1].legend()
    
    # Loss对比
    axes[1, 0].bar(range(1, n_samples+1), loss_values, color='lightgreen', alpha=0.8)
    axes[1, 0].set_title('Loss Comparison Across All Samples')
    axes[1, 0].set_xlabel('Sample ID')
    axes[1, 0].set_ylabel('L1 Loss')
    axes[1, 0].grid(True, alpha=0.3)
    
    mean_loss = np.mean(loss_values)
    axes[1, 0].axhline(y=mean_loss, color='red', linestyle='--',
                       label=f'Mean: {mean_loss:.6f}')
    axes[1, 0].legend()
    
    # 散点图：PSNR vs SSIM
    axes[1, 1].scatter(psnr_values, ssim_values, c=loss_values, 
                       cmap='viridis', s=100, alpha=0.8)
    axes[1, 1].set_xlabel('PSNR (dB)')
    axes[1, 1].set_ylabel('SSIM')
    axes[1, 1].set_title('PSNR vs SSIM (colored by Loss)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(axes[1, 1].collections[0], ax=axes[1, 1])
    cbar.set_label('L1 Loss')
    
    # 标注样本ID
    for i, (psnr, ssim) in enumerate(zip(psnr_values, ssim_values)):
        axes[1, 1].annotate(f'{i+1}', (psnr, ssim), xytext=(5, 5), 
                           textcoords='offset points', fontsize=8)
    
    plt.tight_layout()
    plt.savefig(save_dir / 'all_samples_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 所有样本对比图保存完成")

def main():
    """主函数"""
    all_results = test_all_samples()
    
    if all_results:
        print(f"\n📈 测试统计:")
        psnr_values = [r['psnr'] for r in all_results]
        ssim_values = [r['ssim'] for r in all_results]
        
        print(f"   平均PSNR: {np.mean(psnr_values):.2f} ± {np.std(psnr_values):.2f} dB")
        print(f"   平均SSIM: {np.mean(ssim_values):.4f} ± {np.std(ssim_values):.4f}")
        print(f"   最佳PSNR: {np.max(psnr_values):.2f} dB (样本{np.argmax(psnr_values)+1})")
        print(f"   最差PSNR: {np.min(psnr_values):.2f} dB (样本{np.argmin(psnr_values)+1})")

if __name__ == '__main__':
    main()
