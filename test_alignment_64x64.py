#!/usr/bin/env python3
"""
64×64裁剪图像对齐模块测试脚本
使用训练时的标准尺寸来正确测试HSI-RefSR模型的对齐功能
"""

import time
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import cv2
from scipy.io import loadmat

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模块
from hsirsr.model.refsr import CrossNetHSI

# 复制必要的函数
def minmax_normalize(img):
    """Min-max normalization"""
    return (img - img.min()) / (img.max() - img.min() + 1e-8)

def hwc2chw(img):
    """Convert HWC to CHW format"""
    return img.transpose(2, 0, 1)

def hsi_hwc2chw(img):
    """Convert HSI HWC to CHW format with additional dimension"""
    return img.transpose(2, 0, 1)[None, ...]  # [1, C, H, W]

class SRDegrade:
    """Simple degradation for super-resolution"""
    def __init__(self, scale_factor):
        self.scale_factor = scale_factor
    
    def __call__(self, img):
        h, w = img.shape[:2]
        new_h, new_w = h // self.scale_factor, w // self.scale_factor
        if len(img.shape) == 3:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        else:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)

def create_swir_srf(num_bands):
    """创建适配SWIR数据的光谱响应函数"""
    srf = np.zeros((3, num_bands), dtype=np.float32)
    
    # 为SWIR波段创建简化的RGB映射
    band_per_channel = num_bands // 3
    
    # R通道 - 对应较长波长
    srf[0, :band_per_channel] = np.linspace(0.8, 0.2, band_per_channel)
    
    # G通道 - 对应中等波长  
    start_g = band_per_channel
    end_g = 2 * band_per_channel
    srf[1, start_g:end_g] = np.linspace(0.2, 0.8, band_per_channel)
    
    # B通道 - 对应较短波长
    start_b = 2 * band_per_channel
    srf[2, start_b:] = np.linspace(0.8, 0.2, num_bands - start_b)
    
    return srf

class Alignment64x64Dataset:
    """64×64裁剪数据集类 - 专门用于对齐模块测试"""
    
    def __init__(self, root, input, ref, names_path, sf, crop_size=[64, 64], num_crops=5):
        root = Path(root)
        self.hsi_inp_dir = root / (input+'_hsi') / 'HR'
        self.hsi_rgb_dir = root / input / 'HR'
        self.rgb_dir = root / ref / 'HR'
        
        self.sf = sf
        self.crop_size = crop_size
        self.num_crops = num_crops  # 每个样本生成多少个裁剪
        
        # 读取样本名称
        with open(root / names_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        self.names = names
        
        # 创建适配SWIR数据的SRF
        self.srf = create_swir_srf(273)
        
        # 设置降采样
        self.degrade = SRDegrade(sf)
        
        print(f"64×64对齐测试数据集初始化完成:")
        print(f"  样本数量: {len(self.names)}")
        print(f"  每样本裁剪数: {num_crops}")
        print(f"  裁剪尺寸: {crop_size}")
        print(f"  SRF形状: {self.srf.shape}")
        print(f"  缩放因子: {sf}")

    def get_mat(self, path):
        """加载.mat文件"""
        return loadmat(path)

    def get_png(self, path):
        """加载.png文件"""
        import imageio
        img = imageio.imread(path)
        img = np.array(img).astype('float')
        img = img / 255.0
        return img

    def random_crop(self, img, crop_h, crop_w):
        """随机裁剪"""
        h, w = img.shape[:2]
        if h < crop_h or w < crop_w:
            # 如果图像太小，则填充
            if len(img.shape) == 3:
                padded = np.zeros((max(h, crop_h), max(w, crop_w), img.shape[2]), dtype=img.dtype)
                padded[:h, :w] = img
            else:
                padded = np.zeros((max(h, crop_h), max(w, crop_w)), dtype=img.dtype)
                padded[:h, :w] = img
            img = padded
            h, w = img.shape[:2]
        
        start_h = np.random.randint(0, h - crop_h + 1)
        start_w = np.random.randint(0, w - crop_w + 1)
        
        if len(img.shape) == 3:
            return img[start_h:start_h+crop_h, start_w:start_w+crop_w, :]
        else:
            return img[start_h:start_h+crop_h, start_w:start_w+crop_w]

    def __len__(self):
        return len(self.names) * self.num_crops

    def __getitem__(self, index):
        # 计算样本索引和裁剪索引
        sample_idx = index // self.num_crops
        crop_idx = index % self.num_crops
        
        name = self.names[sample_idx]
        
        # 加载HSI数据 (MAT格式)
        hsi_hr = self.get_mat(str(self.hsi_inp_dir / (name+'.mat')))['gt']
        hsi_hr = minmax_normalize(hsi_hr.astype('float'))
        
        # 加载RGB参考数据 (PNG格式) - 偏移后的ground truth
        rgb_offset_hr = self.get_png(str(self.rgb_dir / (name+'.png')))
        rgb_offset_hr = minmax_normalize(rgb_offset_hr.astype('float'))
        
        # 加载HSI生成的伪RGB (PNG格式)
        hsi_pseudo_rgb_lr = self.get_png(str(self.hsi_rgb_dir / (name+'.png')))
        hsi_pseudo_rgb_lr = minmax_normalize(hsi_pseudo_rgb_lr.astype('float'))
        
        # 使用SRF将HSI转换为RGB
        hsi_rgb_hr = hsi_hr @ self.srf.T
        
        # 确保所有图像尺寸一致
        h, w = hsi_hr.shape[:2]
        
        # 调整RGB图像到HSI尺寸 (如果需要)
        if rgb_offset_hr.shape[:2] != (h, w):
            rgb_offset_hr = cv2.resize(rgb_offset_hr, (w, h), interpolation=cv2.INTER_LINEAR)
        if hsi_rgb_hr.shape[:2] != (h, w):
            hsi_rgb_hr = cv2.resize(hsi_rgb_hr, (w, h), interpolation=cv2.INTER_LINEAR)
        
        # 随机裁剪到64×64
        crop_h, crop_w = self.crop_size
        
        # 设置随机种子确保所有图像裁剪同一区域
        np.random.seed(sample_idx * 1000 + crop_idx)
        
        hsi_hr_crop = self.random_crop(hsi_hr, crop_h, crop_w)
        hsi_rgb_hr_crop = self.random_crop(hsi_rgb_hr, crop_h, crop_w)
        rgb_offset_hr_crop = self.random_crop(rgb_offset_hr, crop_h, crop_w)
        
        # 生成低分辨率版本
        hsi_lr_crop = self.degrade(hsi_hr_crop)
        hsi_rgb_lr_crop = self.degrade(hsi_rgb_hr_crop)
        rgb_offset_lr_crop = self.degrade(rgb_offset_hr_crop)
        
        print(f"裁剪 {crop_idx+1}/{self.num_crops} - 样本: {name}")
        print(f"  原始尺寸: {h}×{w}")
        print(f"  裁剪尺寸: {hsi_hr_crop.shape[:2]}")
        print(f"  LR尺寸: {hsi_lr_crop.shape[:2]}")
        
        # 处理数据格式
        hsi_hr_processed = hsi_hwc2chw(hsi_hr_crop).astype(np.float32)
        hsi_lr_processed = hsi_hwc2chw(hsi_lr_crop).astype(np.float32)
        
        hsi_rgb_hr_processed = hwc2chw(hsi_rgb_hr_crop).astype(np.float32)
        hsi_rgb_lr_processed = hwc2chw(hsi_rgb_lr_crop).astype(np.float32)
        rgb_offset_hr_processed = hwc2chw(rgb_offset_hr_crop).astype(np.float32)
        rgb_offset_lr_processed = hwc2chw(rgb_offset_lr_crop).astype(np.float32)
        
        return {
            'name': f"{name}_crop_{crop_idx+1}",
            'original_name': name,
            'crop_idx': crop_idx,
            'hsi_hr': hsi_hr_processed,                    # [1, 273, 64, 64] - 目标
            'hsi_lr': hsi_lr_processed,                    # [1, 273, 16, 16] - 输入
            'hsi_rgb_hr': hsi_rgb_hr_processed,            # [3, 64, 64] - HSI生成的高分RGB
            'hsi_rgb_lr': hsi_rgb_lr_processed,            # [3, 16, 16] - HSI生成的低分RGB (模型输入)
            'rgb_offset_hr': rgb_offset_hr_processed,      # [3, 64, 64] - 偏移后的ground truth (原始)
            'rgb_offset_lr': rgb_offset_lr_processed,      # [3, 16, 16] - 偏移后的低分RGB (模型输入)
            'crop_shape': (crop_h, crop_w),
            'lr_shape': hsi_lr_crop.shape[:2]
        }

def load_best_model():
    """加载最佳模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
    
    checkpoint = torch.load('saved/step-by-step-training/best_model.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, device

def calculate_alignment_error(img1, img2):
    """计算两张图像的对齐误差"""
    if isinstance(img1, torch.Tensor):
        img1 = img1.detach().cpu().numpy()
    if isinstance(img2, torch.Tensor):
        img2 = img2.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    img1 = np.clip(img1, 0, 1)
    img2 = np.clip(img2, 0, 1)
    
    # 计算像素级差异
    diff = np.abs(img1 - img2)
    mean_error = np.mean(diff)
    max_error = np.max(diff)
    
    return diff, mean_error, max_error

def create_alignment_visualization_64x64(sample_data, model_output, save_dir, sample_idx):
    """创建64×64对齐效果可视化"""
    
    name = sample_data['name']
    hsi_rgb_lr = sample_data['hsi_rgb_lr']              # [3, 16, 16] HSI生成的低分RGB
    rgb_offset_hr = sample_data['rgb_offset_hr']        # [3, 64, 64] 偏移后的ground truth
    rgb_offset_lr = sample_data['rgb_offset_lr']        # [3, 16, 16] 偏移后的低分RGB
    
    # 模型输出
    hsi_sr_output = model_output['hsi_sr']              # [273, 64, 64]
    ref_warp = model_output['ref_warp']                 # [3, 16, 16] 对齐后的RGB
    flow = model_output['flow']                         # [2, 16, 16] 光流
    
    # 转换为numpy并调整维度
    hsi_rgb_lr_np = hsi_rgb_lr.cpu().numpy().transpose(1, 2, 0)         # [16, 16, 3]
    rgb_offset_hr_np = rgb_offset_hr.cpu().numpy().transpose(1, 2, 0)   # [64, 64, 3]
    rgb_offset_lr_np = rgb_offset_lr.cpu().numpy().transpose(1, 2, 0)   # [16, 16, 3]
    ref_warp_np = ref_warp.cpu().numpy().transpose(1, 2, 0)             # [16, 16, 3]
    
    # 计算对齐前后的差异 (在LR尺寸上)
    diff_before, mean_before, max_before = calculate_alignment_error(hsi_rgb_lr_np, rgb_offset_lr_np)
    diff_after, mean_after, max_after = calculate_alignment_error(hsi_rgb_lr_np, ref_warp_np)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 5, figsize=(25, 10))
    fig.suptitle(f'{name} - 64×64 Alignment Analysis\n'
                 f'Alignment Improvement: {((mean_before - mean_after) / mean_before * 100):.1f}% | '
                 f'Before: {mean_before:.4f} → After: {mean_after:.4f}', 
                 fontsize=16, fontweight='bold')
    
    # 第一行：输入和对齐过程
    # 1. HSI生成的RGB (LR)
    axes[0, 0].imshow(np.clip(hsi_rgb_lr_np, 0, 1))
    axes[0, 0].set_title(f'HSI RGB (LR)\n16×16')
    axes[0, 0].axis('off')
    
    # 2. 偏移RGB (LR) - 对齐前
    axes[0, 1].imshow(np.clip(rgb_offset_lr_np, 0, 1))
    axes[0, 1].set_title(f'Offset RGB (LR)\n16×16 - Before Alignment')
    axes[0, 1].axis('off')
    
    # 3. 对齐后的RGB
    axes[0, 2].imshow(np.clip(ref_warp_np, 0, 1))
    axes[0, 2].set_title(f'Aligned RGB\n16×16 - After Alignment')
    axes[0, 2].axis('off')
    
    # 4. 偏移RGB (HR) - Ground Truth
    axes[0, 3].imshow(np.clip(rgb_offset_hr_np, 0, 1))
    axes[0, 3].set_title(f'Ground Truth RGB (HR)\n64×64')
    axes[0, 3].axis('off')
    
    # 5. 光流可视化
    flow_np = flow.cpu().numpy()  # [2, 16, 16]
    flow_magnitude = np.sqrt(flow_np[0]**2 + flow_np[1]**2)
    im_flow = axes[0, 4].imshow(flow_magnitude, cmap='jet')
    axes[0, 4].set_title('Optical Flow Magnitude')
    axes[0, 4].axis('off')
    plt.colorbar(im_flow, ax=axes[0, 4], fraction=0.046)
    
    # 第二行：差异分析和超分结果
    # 1. 对齐前差异
    im1 = axes[1, 0].imshow(diff_before, cmap='hot', vmin=0, vmax=0.3)
    axes[1, 0].set_title(f'Difference Before\nMean: {mean_before:.4f}')
    axes[1, 0].axis('off')
    plt.colorbar(im1, ax=axes[1, 0], fraction=0.046)
    
    # 2. 对齐后差异
    im2 = axes[1, 1].imshow(diff_after, cmap='hot', vmin=0, vmax=0.3)
    axes[1, 1].set_title(f'Difference After\nMean: {mean_after:.4f}')
    axes[1, 1].axis('off')
    plt.colorbar(im2, ax=axes[1, 1], fraction=0.046)
    
    # 3. HSI超分结果 (选择中间波段)
    mid_band = hsi_sr_output.shape[0] // 2
    hsi_sr_band = hsi_sr_output[mid_band].cpu().numpy()
    im3 = axes[1, 2].imshow(hsi_sr_band, cmap='viridis', vmin=0, vmax=1)
    axes[1, 2].set_title(f'HSI SR Band {mid_band+1}\n64×64')
    axes[1, 2].axis('off')
    plt.colorbar(im3, ax=axes[1, 2], fraction=0.046)
    
    # 4. HSI SR转RGB
    srf = create_swir_srf(273)
    hsi_sr_np = hsi_sr_output.cpu().numpy().transpose(1, 2, 0)  # [64, 64, 273]
    hsi_sr_rgb = hsi_sr_np @ srf.T  # [64, 64, 3]
    axes[1, 3].imshow(np.clip(hsi_sr_rgb, 0, 1))
    axes[1, 3].set_title(f'HSI SR → RGB\n64×64')
    axes[1, 3].axis('off')
    
    # 5. 统计信息
    axes[1, 4].axis('off')
    improvement_text = f"""
Alignment Results:
• Error Reduction: {((mean_before - mean_after) / mean_before * 100):.1f}%
• Before: {mean_before:.4f}
• After: {mean_after:.4f}
• Max Error Before: {max_before:.4f}
• Max Error After: {max_after:.4f}

Flow Statistics:
• Mean Magnitude: {np.mean(flow_magnitude):.3f}
• Max Magnitude: {np.max(flow_magnitude):.3f}

Processing Info:
• Input Size: 16×16
• Output Size: 64×64
• Scale Factor: 4x
• HSI Bands: 273

Quality Metrics:
• Final RGB Quality: {np.mean(np.abs(hsi_sr_rgb - rgb_offset_hr_np)):.4f}
    """
    
    axes[1, 4].text(0.05, 0.95, improvement_text, transform=axes[1, 4].transAxes, 
                    fontsize=9, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_dir / f'{name}_alignment_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return {
        'mean_error_before': mean_before,
        'mean_error_after': mean_after,
        'max_error_before': max_before,
        'max_error_after': max_after,
        'improvement_percent': (mean_before - mean_after) / mean_before * 100,
        'flow_magnitude_mean': np.mean(flow_magnitude),
        'flow_magnitude_max': np.max(flow_magnitude),
        'final_rgb_quality': np.mean(np.abs(hsi_sr_rgb - rgb_offset_hr_np))
    }

def test_alignment_64x64():
    """测试64×64对齐模块"""
    print("🔍 开始64×64对齐模块测试")
    print("=" * 80)
    
    # 加载模型
    model, device = load_best_model()
    print(f"✅ 模型加载完成，使用设备: {device}")
    
    # 创建64×64数据集
    dataset = Alignment64x64Dataset(
        root='data/daogu_processed',
        input='img1',
        ref='img2', 
        names_path='test.txt',
        sf=4,
        crop_size=[64, 64],
        num_crops=3  # 每个样本生成3个裁剪
    )
    
    print(f"📊 测试数据集: {len(dataset)} 个裁剪 (来自 {len(dataset.names)} 个样本)")
    
    # 创建保存目录
    save_dir = Path('alignment_64x64_results')
    save_dir.mkdir(exist_ok=True)
    
    # 存储所有结果
    all_results = []
    criterion = nn.L1Loss()
    
    print("\n🚀 开始逐一测试所有裁剪...")
    
    with torch.no_grad():
        for sample_idx in range(min(9, len(dataset))):  # 测试前9个裁剪 (3个样本×3个裁剪)
            print(f"\n--- 测试裁剪 {sample_idx+1}/{len(dataset)} ---")
            
            try:
                # 获取样本数据
                sample_data = dataset[sample_idx]
                name = sample_data['name']
                
                print(f"  裁剪名称: {name}")
                print(f"  原始样本: {sample_data['original_name']}")
                print(f"  裁剪尺寸: {sample_data['crop_shape']}")
                print(f"  LR尺寸: {sample_data['lr_shape']}")
                
                # 移动数据到GPU
                hsi_hr = torch.from_numpy(sample_data['hsi_hr']).unsqueeze(0).to(device)
                hsi_lr = torch.from_numpy(sample_data['hsi_lr']).unsqueeze(0).to(device)
                hsi_rgb_lr = torch.from_numpy(sample_data['hsi_rgb_lr']).unsqueeze(0).to(device)
                rgb_offset_lr = torch.from_numpy(sample_data['rgb_offset_lr']).unsqueeze(0).to(device)
                
                print(f"  数据形状: HSI_LR={hsi_lr.shape}, HSI_RGB_LR={hsi_rgb_lr.shape}, RGB_LR={rgb_offset_lr.shape}")
                
                # 前向传播 - 测试对齐模块
                start_time = time.time()
                hsi_sr_output, ref_warp, flow, masks = model(
                    hsi_sr=hsi_lr, 
                    hsi_rgb_sr=hsi_rgb_lr, 
                    ref_hr=rgb_offset_lr  # 使用LR尺寸的RGB
                )
                inference_time = time.time() - start_time
                
                # 计算损失
                loss = criterion(hsi_sr_output, hsi_hr)
                
                print(f"  ✅ 推理完成，Loss: {loss.item():.6f}")
                print(f"  ⏱️ 推理时间: {inference_time:.3f}s")
                print(f"  📐 输出尺寸: HSI_SR={hsi_sr_output.shape}, REF_WARP={ref_warp.shape}, FLOW={flow.shape}")
                
                # 准备可视化数据
                model_output = {
                    'hsi_sr': hsi_sr_output[0],      # [1, 273, 64, 64] -> [273, 64, 64]
                    'ref_warp': ref_warp[0],         # [1, 3, 16, 16] -> [3, 16, 16]
                    'flow': flow[0],                 # [1, 2, 16, 16] -> [2, 16, 16]
                    'masks': masks[0] if masks is not None else None
                }
                
                # 生成对齐效果可视化
                print(f"  📊 生成对齐效果分析...")
                alignment_stats = create_alignment_visualization_64x64(
                    sample_data, 
                    model_output, 
                    save_dir, 
                    sample_idx
                )
                
                # 保存结果
                result = {
                    'sample_idx': sample_idx,
                    'name': name,
                    'original_name': sample_data['original_name'],
                    'crop_idx': sample_data['crop_idx'],
                    'crop_shape': sample_data['crop_shape'],
                    'lr_shape': sample_data['lr_shape'],
                    'loss': loss.item(),
                    'inference_time': inference_time,
                    'alignment_stats': alignment_stats
                }
                
                all_results.append(result)
                
                print(f"  📈 对齐改善: {alignment_stats['improvement_percent']:.1f}%")
                print(f"  📊 最终RGB质量: {alignment_stats['final_rgb_quality']:.4f}")
                
                # 清理GPU缓存
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"  ❌ 裁剪 {sample_idx+1} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                continue
    
    print(f"\n🎉 64×64对齐测试完成！")
    print(f"📁 结果保存在: {save_dir}")
    print(f"📊 共测试 {len(all_results)} 个裁剪")
    
    return all_results

def main():
    """主函数"""
    all_results = test_alignment_64x64()
    
    if all_results:
        alignment_improvements = [r['alignment_stats']['improvement_percent'] for r in all_results]
        final_qualities = [r['alignment_stats']['final_rgb_quality'] for r in all_results]
        flow_magnitudes = [r['alignment_stats']['flow_magnitude_mean'] for r in all_results]
        
        print(f"\n📈 64×64对齐测试统计:")
        print(f"   平均对齐改善: {np.mean(alignment_improvements):.2f}% ± {np.std(alignment_improvements):.2f}%")
        print(f"   平均最终RGB质量: {np.mean(final_qualities):.4f} ± {np.std(final_qualities):.4f}")
        print(f"   平均光流强度: {np.mean(flow_magnitudes):.3f} ± {np.std(flow_magnitudes):.3f}")
        print(f"   最佳对齐改善: {np.max(alignment_improvements):.2f}% (裁剪{np.argmax(alignment_improvements)+1})")
        
        # 按样本分组统计
        sample_groups = {}
        for r in all_results:
            original_name = r['original_name']
            if original_name not in sample_groups:
                sample_groups[original_name] = []
            sample_groups[original_name].append(r)
        
        print(f"\n📊 按样本分组统计:")
        for sample_name, results in sample_groups.items():
            improvements = [r['alignment_stats']['improvement_percent'] for r in results]
            qualities = [r['alignment_stats']['final_rgb_quality'] for r in results]
            print(f"   {sample_name}: 对齐改善 {np.mean(improvements):.1f}%, RGB质量 {np.mean(qualities):.4f}")

if __name__ == '__main__':
    main()
