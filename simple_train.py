#!/usr/bin/env python3
"""
简单的DaoGu数据集HSI-RefSR模型训练脚本
"""

import os
import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
import numpy as np
from skimage.metrics import peak_signal_noise_ratio, structural_similarity

# 导入必要的模块
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

def calculate_psnr(img1, img2):
    """计算PSNR"""
    if isinstance(img1, torch.Tensor):
        img1 = img1.detach().cpu().numpy()
    if isinstance(img2, torch.Tensor):
        img2 = img2.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    img1 = np.clip(img1, 0, 1)
    img2 = np.clip(img2, 0, 1)
    
    return peak_signal_noise_ratio(img1, img2, data_range=1.0)

def calculate_ssim(img1, img2):
    """计算SSIM"""
    if isinstance(img1, torch.Tensor):
        img1 = img1.detach().cpu().numpy()
    if isinstance(img2, torch.Tensor):
        img2 = img2.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    img1 = np.clip(img1, 0, 1)
    img2 = np.clip(img2, 0, 1)
    
    # 对于多通道图像，计算平均SSIM
    if len(img1.shape) > 2:
        ssim_values = []
        for i in range(img1.shape[0]):
            ssim_val = structural_similarity(img1[i], img2[i], data_range=1.0)
            ssim_values.append(ssim_val)
        return np.mean(ssim_values)
    else:
        return structural_similarity(img1, img2, data_range=1.0)

def train_epoch(model, train_loader, optimizer, criterion, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    total_psnr = 0.0
    total_ssim = 0.0
    num_batches = len(train_loader)
    
    print(f"\n=== Epoch {epoch} 训练 ===")
    
    for batch_idx, batch in enumerate(train_loader):
        # 移动数据到GPU
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
        hsi_hr = hsi_hr.to(device)
        hsi_lr = hsi_lr.to(device)
        hsi_rgb_lr = hsi_rgb_lr.to(device)
        rgb_hr = rgb_hr.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
        
        # 计算损失
        loss = criterion(output, hsi_hr)
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 计算指标
        with torch.no_grad():
            psnr = calculate_psnr(output[0, 0, 100].cpu(), hsi_hr[0, 0, 100].cpu())
            ssim = calculate_ssim(output[0, 0, 100].cpu(), hsi_hr[0, 0, 100].cpu())
        
        total_loss += loss.item()
        total_psnr += psnr
        total_ssim += ssim
        
        # 打印进度
        if batch_idx % 5 == 0:
            print(f"  Batch {batch_idx+1}/{num_batches}: "
                  f"Loss={loss.item():.6f}, PSNR={psnr:.2f}dB, SSIM={ssim:.4f}")
    
    avg_loss = total_loss / num_batches
    avg_psnr = total_psnr / num_batches
    avg_ssim = total_ssim / num_batches
    
    print(f"训练完成 - 平均Loss: {avg_loss:.6f}, 平均PSNR: {avg_psnr:.2f}dB, 平均SSIM: {avg_ssim:.4f}")
    
    return avg_loss, avg_psnr, avg_ssim

def validate_epoch(model, val_loader, criterion, device, epoch):
    """验证一个epoch"""
    model.eval()
    total_loss = 0.0
    total_psnr = 0.0
    total_ssim = 0.0
    num_batches = len(val_loader)
    
    print(f"\n=== Epoch {epoch} 验证 ===")
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            # 移动数据到GPU
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            # 前向传播
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            
            # 计算损失
            loss = criterion(output, hsi_hr)
            
            # 计算指标
            psnr = calculate_psnr(output[0, 0, 100].cpu(), hsi_hr[0, 0, 100].cpu())
            ssim = calculate_ssim(output[0, 0, 100].cpu(), hsi_hr[0, 0, 100].cpu())
            
            total_loss += loss.item()
            total_psnr += psnr
            total_ssim += ssim
    
    avg_loss = total_loss / num_batches
    avg_psnr = total_psnr / num_batches
    avg_ssim = total_ssim / num_batches
    
    print(f"验证完成 - 平均Loss: {avg_loss:.6f}, 平均PSNR: {avg_psnr:.2f}dB, 平均SSIM: {avg_ssim:.4f}")
    
    return avg_loss, avg_psnr, avg_ssim

def main():
    """主训练函数"""
    print("=" * 80)
    print("🚀 DaoGu数据集HSI-RefSR模型简单训练")
    print("=" * 80)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载配置
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建数据集
    print("\n=== 创建数据集 ===")
    train_cfg = config['train']['dataset'].copy()
    train_cfg.pop('type@', None)
    train_dataset = DaoGuSRFDataset(**train_cfg)

    test_cfg = config['test']['dataset'].copy()
    test_cfg.pop('type@', None)
    test_dataset = DaoGuSRFDataset(**test_cfg)
    
    print(f"训练样本数量: {len(train_dataset)}")
    print(f"测试样本数量: {len(test_dataset)}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=1,  # 固定为1避免内存问题
        shuffle=True,
        num_workers=0,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        test_dataset,
        batch_size=1,
        shuffle=False,
        num_workers=0,
        pin_memory=True
    )
    
    # 创建模型
    print("\n=== 创建模型 ===")
    model = CrossNetHSI(
        use_mask=config['module']['model']['use_mask'],
        use_pwc=config['module']['model']['use_pwc'],
        reweight=config['module']['model']['reweight']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数数量: {total_params:,}")
    
    # 创建优化器和损失函数
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['module']['optimizer']['lr'],
        weight_decay=config['module']['optimizer']['weight_decay']
    )
    
    criterion = nn.L1Loss()
    
    # 创建保存目录
    save_dir = Path('saved/daogu-simple-training')
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # 训练循环
    print("\n=== 开始训练 ===")
    max_epochs = 20  # 减少训练轮数用于测试
    best_psnr = 0.0
    
    for epoch in range(1, max_epochs + 1):
        start_time = time.time()
        
        # 训练
        train_loss, train_psnr, train_ssim = train_epoch(
            model, train_loader, optimizer, criterion, device, epoch
        )
        
        # 验证
        val_loss, val_psnr, val_ssim = validate_epoch(
            model, val_loader, criterion, device, epoch
        )
        
        epoch_time = time.time() - start_time
        
        print(f"\nEpoch {epoch} 完成 (用时: {epoch_time:.1f}s)")
        print(f"训练 - Loss: {train_loss:.6f}, PSNR: {train_psnr:.2f}dB, SSIM: {train_ssim:.4f}")
        print(f"验证 - Loss: {val_loss:.6f}, PSNR: {val_psnr:.2f}dB, SSIM: {val_ssim:.4f}")
        
        # 保存最佳模型
        if val_psnr > best_psnr:
            best_psnr = val_psnr
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_psnr': best_psnr,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'val_psnr': val_psnr,
                'val_ssim': val_ssim,
            }, save_dir / 'best_model.pth')
            print(f"✅ 保存最佳模型 (PSNR: {best_psnr:.2f}dB)")
        
        # 每5个epoch保存一次
        if epoch % 5 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_psnr': best_psnr,
            }, save_dir / f'checkpoint_epoch_{epoch}.pth')
            print(f"💾 保存检查点: epoch_{epoch}")
        
        print("-" * 80)
    
    print(f"\n🎉 训练完成！最佳PSNR: {best_psnr:.2f}dB")
    print(f"模型保存在: {save_dir}")

if __name__ == '__main__':
    main()
