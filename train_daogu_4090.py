#!/usr/bin/env python3
"""
DaoGu数据集HSI-RefSR模型训练脚本 - 4090优化版
专为RTX 4090设计，支持原尺寸图像训练，无裁切
"""

import os
import sys
import argparse
import time
from pathlib import Path
import yaml
import torch
import torch.nn as nn
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入必要的模块
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

def print_gpu_info():
    """打印GPU信息"""
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU设备: {gpu_name}")
        print(f"GPU内存: {gpu_memory:.1f} GB")
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        print("✅ GPU缓存已清理")
    else:
        print("❌ 未检测到CUDA设备")
        return False
    return True

def create_data_loaders(config):
    """创建训练和测试数据加载器"""
    print("=== 创建数据加载器 ===")
    
    # 训练数据集
    train_dataset = DaoGuSRFDataset(
        root=config['train']['dataset']['root'],
        input=config['train']['dataset']['input'],
        ref=config['train']['dataset']['ref'],
        names_path=config['train']['dataset']['names_path'],
        sf=config['train']['dataset']['sf'],
        crop_size=config['train']['dataset']['crop_size'],  # None for full size
        repeat=config['train']['dataset']['repeat'],
        use_cache=config['train']['dataset']['use_cache']
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['train']['loader']['batch_size'],
        shuffle=config['train']['loader']['shuffle'],
        num_workers=config['train']['loader']['num_workers'],
        pin_memory=config['train']['loader']['pin_memory']
    )
    
    # 测试数据集
    test_dataset = DaoGuSRFDataset(
        root=config['test']['dataset']['root'],
        input=config['test']['dataset']['input'],
        ref=config['test']['dataset']['ref'],
        names_path=config['test']['dataset']['names_path'],
        sf=config['test']['dataset']['sf'],
        crop_size=config['test']['dataset']['crop_size'],  # None for full size
        use_cache=config['test']['dataset']['use_cache']
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['test']['loader']['batch_size'],
        shuffle=config['test']['loader']['shuffle']
    )
    
    print(f"训练样本数量: {len(train_dataset)}")
    print(f"测试样本数量: {len(test_dataset)}")
    print(f"训练批次数量: {len(train_loader)}")
    print(f"测试批次数量: {len(test_loader)}")
    
    return train_loader, test_loader

def create_model_and_optimizer(config):
    """创建模型和优化器"""
    print("=== 创建模型和优化器 ===")
    
    # 创建模型
    model = CrossNetHSI(
        use_mask=config['module']['model']['use_mask'],
        use_pwc=config['module']['model']['use_pwc'],
        reweight=config['module']['model']['reweight']
    )
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    print(f"模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config['module']['optimizer']['lr'],
        weight_decay=config['module']['optimizer']['weight_decay']
    )
    
    return model, optimizer, device

def train_epoch(model, train_loader, optimizer, criterion, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    num_batches = len(train_loader)
    
    print(f"\n--- Epoch {epoch} 训练开始 ---")
    
    for batch_idx, batch_data in enumerate(train_loader):
        try:
            # 解包数据
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch_data
            
            # 移动到GPU
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_lr = rgb_lr.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            output = model(hsi_lr, hsi_rgb_lr, rgb_lr)
            
            # 计算损失
            loss = criterion(output, hsi_hr)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            # 更新参数
            optimizer.step()
            
            total_loss += loss.item()
            
            # 打印进度
            if (batch_idx + 1) % 5 == 0 or batch_idx == 0:
                print(f"  Batch {batch_idx+1}/{num_batches}, Loss: {loss.item():.6f}")
                
                # 打印GPU内存使用情况
                if torch.cuda.is_available():
                    memory_used = torch.cuda.memory_allocated() / 1024**3
                    memory_cached = torch.cuda.memory_reserved() / 1024**3
                    print(f"    GPU内存: {memory_used:.2f}GB 已用, {memory_cached:.2f}GB 缓存")
            
        except Exception as e:
            print(f"❌ Batch {batch_idx} 训练失败: {e}")
            # 清理GPU内存
            torch.cuda.empty_cache()
            continue
    
    avg_loss = total_loss / num_batches
    print(f"Epoch {epoch} 平均损失: {avg_loss:.6f}")
    
    return avg_loss

def validate_epoch(model, test_loader, criterion, device, epoch):
    """验证一个epoch"""
    model.eval()
    total_loss = 0.0
    num_batches = len(test_loader)
    
    print(f"\n--- Epoch {epoch} 验证开始 ---")
    
    with torch.no_grad():
        for batch_idx, batch_data in enumerate(test_loader):
            try:
                # 解包数据
                hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch_data
                
                # 移动到GPU
                hsi_hr = hsi_hr.to(device)
                hsi_lr = hsi_lr.to(device)
                hsi_rgb_lr = hsi_rgb_lr.to(device)
                rgb_lr = rgb_lr.to(device)
                
                # 前向传播
                output = model(hsi_lr, hsi_rgb_lr, rgb_lr)
                
                # 计算损失
                loss = criterion(output, hsi_hr)
                total_loss += loss.item()
                
                if batch_idx == 0:
                    print(f"  验证样本形状: HSI_HR {hsi_hr.shape}, Output {output.shape}")
                
            except Exception as e:
                print(f"❌ 验证 Batch {batch_idx} 失败: {e}")
                continue
    
    avg_loss = total_loss / num_batches if num_batches > 0 else float('inf')
    print(f"Epoch {epoch} 验证损失: {avg_loss:.6f}")
    
    return avg_loss

def save_checkpoint(model, optimizer, epoch, loss, save_dir, is_best=False):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
    }
    
    # 保存最新检查点
    latest_path = save_dir / 'latest_checkpoint.pth'
    torch.save(checkpoint, latest_path)
    
    # 保存最佳模型
    if is_best:
        best_path = save_dir / 'best_model.pth'
        torch.save(checkpoint, best_path)
        print(f"✅ 最佳模型已保存: {best_path}")
    
    # 定期保存
    if epoch % 10 == 0:
        epoch_path = save_dir / f'checkpoint_epoch_{epoch}.pth'
        torch.save(checkpoint, epoch_path)

def train_model(config, save_dir):
    """主训练函数"""
    print("=== 开始训练 ===")
    
    # 检查GPU
    if not print_gpu_info():
        return
    
    # 创建保存目录
    save_dir = Path(save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建数据加载器
    train_loader, test_loader = create_data_loaders(config)
    
    # 创建模型和优化器
    model, optimizer, device = create_model_and_optimizer(config)
    
    # 损失函数
    criterion = nn.L1Loss()
    
    # 训练参数
    max_epochs = config['engine']['max_epochs']
    best_loss = float('inf')
    
    print(f"\n🚀 开始训练，最大轮数: {max_epochs}")
    print("=" * 80)
    
    for epoch in range(1, max_epochs + 1):
        start_time = time.time()
        
        # 训练
        train_loss = train_epoch(model, train_loader, optimizer, criterion, device, epoch)
        
        # 验证
        val_loss = validate_epoch(model, test_loader, criterion, device, epoch)
        
        # 保存检查点
        is_best = val_loss < best_loss
        if is_best:
            best_loss = val_loss
        
        save_checkpoint(model, optimizer, epoch, val_loss, save_dir, is_best)
        
        epoch_time = time.time() - start_time
        print(f"Epoch {epoch} 完成，用时: {epoch_time:.2f}秒")
        print("=" * 80)
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
    
    print(f"🎉 训练完成！最佳验证损失: {best_loss:.6f}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DaoGu HSI-RefSR 4090训练')
    parser.add_argument('--config', default='daogu_config.yaml', help='配置文件路径')
    parser.add_argument('--save_dir', default='saved/daogu-4090-training', help='模型保存目录')
    args = parser.parse_args()
    
    print("=" * 80)
    print("🚀 DaoGu数据集HSI-RefSR模型训练 - 4090优化版")
    print("=" * 80)
    print(f"配置文件: {args.config}")
    print(f"保存目录: {args.save_dir}")
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print(f"最大训练轮数: {config['engine']['max_epochs']}")
    print(f"学习率: {config['module']['optimizer']['lr']}")
    print(f"批大小: {config['train']['loader']['batch_size']}")
    print(f"裁切尺寸: {config['train']['dataset']['crop_size']} (None=原尺寸)")
    
    # 开始训练
    train_model(config, args.save_dir)

if __name__ == '__main__':
    main()
