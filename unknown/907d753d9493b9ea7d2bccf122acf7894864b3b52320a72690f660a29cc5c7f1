#!/usr/bin/env python3
"""
调试版本的DaoGu数据集HSI-RefSR模型训练脚本
逐步测试每个组件
"""

import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
import numpy as np

def test_imports():
    """测试所有必要的导入"""
    print("=" * 50)
    print("🔍 测试导入...")
    
    try:
        from daogu_dataset import DaoGuSRFDataset
        print("✅ DaoGuSRFDataset 导入成功")
    except Exception as e:
        print(f"❌ DaoGuSRFDataset 导入失败: {e}")
        return False
    
    try:
        from hsirsr.model.refsr import CrossNetHSI
        print("✅ CrossNetHSI 导入成功")
    except Exception as e:
        print(f"❌ CrossNetHSI 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置文件加载"""
    print("=" * 50)
    print("🔍 测试配置文件...")
    
    try:
        with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ 配置文件加载成功")
        print(f"  训练数据集根目录: {config['train']['dataset']['root']}")
        print(f"  测试数据集根目录: {config['test']['dataset']['root']}")
        return config
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None

def test_datasets(config):
    """测试数据集创建和加载"""
    print("=" * 50)
    print("🔍 测试数据集...")

    train_dataset = None
    test_dataset = None

    try:
        from daogu_dataset import DaoGuSRFDataset

        # 创建训练数据集
        train_cfg = config['train']['dataset'].copy()
        train_cfg.pop('type@', None)
        train_dataset = DaoGuSRFDataset(**train_cfg)
        print(f"✅ 训练数据集创建成功，包含 {len(train_dataset)} 个样本")

        # 创建测试数据集
        test_cfg = config['test']['dataset'].copy()
        test_cfg.pop('type@', None)
        test_dataset = DaoGuSRFDataset(**test_cfg)
        print(f"✅ 测试数据集创建成功，包含 {len(test_dataset)} 个样本")

        # 测试加载一个样本
        sample = train_dataset[0]
        print("✅ 样本加载成功:")
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample
        print(f"  hsi_hr: {hsi_hr.shape} ({hsi_hr.dtype})")
        print(f"  hsi_lr: {hsi_lr.shape} ({hsi_lr.dtype})")
        print(f"  hsi_rgb_hr: {hsi_rgb_hr.shape} ({hsi_rgb_hr.dtype})")
        print(f"  hsi_rgb_lr: {hsi_rgb_lr.shape} ({hsi_rgb_lr.dtype})")
        print(f"  rgb_hr: {rgb_hr.shape} ({rgb_hr.dtype})")
        print(f"  rgb_lr: {rgb_lr.shape} ({rgb_lr.dtype})")

        return train_dataset, test_dataset

    except Exception as e:
        print(f"❌ 数据集测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_dataloader(train_dataset, test_dataset):
    """测试数据加载器"""
    print("=" * 50)
    print("🔍 测试数据加载器...")
    
    try:
        train_loader = DataLoader(
            train_dataset,
            batch_size=1,
            shuffle=True,
            num_workers=0,
            pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=1,
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )
        
        print("✅ 数据加载器创建成功")
        
        # 测试一个批次
        for batch in train_loader:
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            print("✅ 批次数据加载成功:")
            print(f"  batch hsi_hr: {hsi_hr.shape}")
            print(f"  batch hsi_lr: {hsi_lr.shape}")
            print(f"  batch rgb_hr: {rgb_hr.shape}")
            break
            
        return train_loader, test_loader
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_model(config):
    """测试模型创建"""
    print("=" * 50)
    print("🔍 测试模型...")
    
    try:
        from hsirsr.model.refsr import CrossNetHSI
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        model = CrossNetHSI(
            use_mask=config['module']['model']['use_mask'],
            use_pwc=config['module']['model']['use_pwc'],
            reweight=config['module']['model']['reweight']
        ).to(device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"✅ 模型创建成功，参数数量: {total_params:,}")
        
        return model, device
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_forward_pass(model, train_loader, device):
    """测试前向传播"""
    print("=" * 50)
    print("🔍 测试前向传播...")
    
    try:
        model.eval()
        
        for batch in train_loader:
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            
            # 移动到设备
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            print(f"输入数据形状:")
            print(f"  hsi_lr: {hsi_lr.shape}")
            print(f"  hsi_rgb_lr: {hsi_rgb_lr.shape}")
            print(f"  rgb_hr: {rgb_hr.shape}")
            
            with torch.no_grad():
                output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
                
            print(f"✅ 前向传播成功:")
            print(f"  输出形状: {output.shape}")
            print(f"  目标形状: {hsi_hr.shape}")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ 前向传播测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_step(model, train_loader, device, config):
    """测试训练步骤"""
    print("=" * 50)
    print("🔍 测试训练步骤...")
    
    try:
        model.train()
        
        # 创建优化器和损失函数
        optimizer = optim.AdamW(
            model.parameters(),
            lr=config['module']['optimizer']['lr'],
            weight_decay=config['module']['optimizer']['weight_decay']
        )
        
        criterion = nn.L1Loss()
        
        print("✅ 优化器和损失函数创建成功")
        
        for batch in train_loader:
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            
            # 移动到设备
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            
            # 计算损失
            loss = criterion(output, hsi_hr)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            print(f"✅ 训练步骤成功:")
            print(f"  损失值: {loss.item():.6f}")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ 训练步骤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数 - 逐步测试所有组件"""
    print("🚀 DaoGu数据集HSI-RefSR模型调试测试")
    print("=" * 80)
    
    # 1. 测试导入
    if not test_imports():
        print("❌ 导入测试失败，退出")
        return
    
    # 2. 测试配置
    config = test_config()
    if config is None:
        print("❌ 配置测试失败，退出")
        return
    
    # 3. 测试数据集
    train_dataset, test_dataset = test_datasets(config)
    if train_dataset is None:
        print("❌ 数据集测试失败，退出")
        return
    
    # 4. 测试数据加载器
    train_loader, test_loader = test_dataloader(train_dataset, test_dataset)
    if train_loader is None:
        print("❌ 数据加载器测试失败，退出")
        return
    
    # 5. 测试模型
    model, device = test_model(config)
    if model is None:
        print("❌ 模型测试失败，退出")
        return
    
    # 6. 测试前向传播
    if not test_forward_pass(model, train_loader, device):
        print("❌ 前向传播测试失败，退出")
        return
    
    # 7. 测试训练步骤
    if not test_training_step(model, train_loader, device, config):
        print("❌ 训练步骤测试失败，退出")
        return
    
    print("=" * 80)
    print("🎉 所有组件测试通过！可以开始正式训练")
    print("=" * 80)

if __name__ == '__main__':
    main()
