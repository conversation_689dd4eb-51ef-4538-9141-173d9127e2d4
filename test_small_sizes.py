#!/usr/bin/env python3
"""
测试小尺寸FlowNet支持
"""

import torch
import numpy as np
from hsirsr.model.refsr import CrossNetHSI

def test_size(h, w):
    """测试特定尺寸"""
    print(f"\n测试尺寸: {h}x{w}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 清理GPU内存
    torch.cuda.empty_cache()
    
    try:
        # 创建模型
        model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
        model.eval()
        
        # 创建测试数据
        hsi_lr = torch.randn(1, 1, 273, h, w).to(device)
        hsi_rgb_lr = torch.randn(1, 3, h, w).to(device)
        rgb_lr = torch.randn(1, 3, h, w).to(device)
        
        print(f"  输入形状: HSI {hsi_lr.shape}, HSI_RGB {hsi_rgb_lr.shape}, RGB {rgb_lr.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = model(hsi_lr, hsi_rgb_lr, rgb_lr)
        
        print(f"  ✅ 成功! 输出形状: {output.shape}")
        
        # 清理
        del model, hsi_lr, hsi_rgb_lr, rgb_lr, output
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"  ❌ 失败: {e}")
        
        # 清理
        torch.cuda.empty_cache()
        return False

def main():
    print("🔍 测试小尺寸FlowNet支持")
    print("=" * 50)
    
    # 测试小尺寸
    test_sizes = [
        (64, 64),     # 最小
        (96, 96),     # 稍大
        (128, 128),   # 中等
        (160, 160),   # 较大
        (192, 192),   # 更大
        (224, 224),   # 再大
        (128, 160),   # 矩形
        (160, 192),   # 矩形
        (192, 224),   # 矩形
        (224, 256),   # 矩形
    ]
    
    successful_sizes = []
    
    for h, w in test_sizes:
        if test_size(h, w):
            successful_sizes.append((h, w))
    
    print(f"\n📊 测试结果:")
    print(f"成功的尺寸: {successful_sizes}")
    
    if successful_sizes:
        print(f"\n✅ 推荐使用的尺寸:")
        for h, w in successful_sizes:
            print(f"  {h}x{w}")
            
        # 找到最大的成功尺寸
        max_area = 0
        best_size = None
        for h, w in successful_sizes:
            area = h * w
            if area > max_area:
                max_area = area
                best_size = (h, w)
        
        if best_size:
            print(f"\n🎯 推荐的最大尺寸: {best_size[0]}x{best_size[1]} (面积: {max_area})")
    else:
        print(f"\n❌ 没有找到可用的尺寸")

if __name__ == '__main__':
    main()
