#!/usr/bin/env python3
"""
使用DaoGu数据集训练HSI-RefSR模型
基于原始的run_refsr.py框架
"""

import os
import sys
import argparse
from pathlib import Path
import yaml

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from torchlight.trainer import config
from torchlight.trainer.entry import run_lazy, DataSource
from torchlight.utils.helper import get_obj
from torchlight.utils.reproducibility import SeedDataLoader, setup_randomness

# 导入模块
import hsirsr.module as module
import hsirsr.data as dataset

# 确保DaoGu数据集可用
from daogu_dataset import DaoGuSRFDataset

class DaoGuDataSource(DataSource):
    """DaoGu数据源"""
    def __init__(self, cfg):
        super().__init__()
        self.cfg = cfg

    def train_loader(self):
        """创建训练数据加载器"""
        print("=== 创建训练数据加载器 ===")
        
        # 直接创建DaoGu数据集
        train_dataset = DaoGuSRFDataset(
            root=self.cfg.train.dataset.root,
            names_path=self.cfg.train.dataset.names_path,
            sf=self.cfg.train.dataset.sf,
            crop_size=self.cfg.train.dataset.crop_size,
            repeat=self.cfg.train.dataset.repeat,
            use_cache=self.cfg.train.dataset.use_cache
        )
        
        train_loader = SeedDataLoader(train_dataset, **self.cfg.train.loader)
        print(f"训练样本数量: {len(train_dataset)}")
        print(f"训练批次数量: {len(train_loader)}")
        return train_loader

    def test_loader(self):
        """创建测试数据加载器"""
        print("=== 创建测试数据加载器 ===")
        
        # 直接创建DaoGu数据集
        test_dataset = DaoGuSRFDataset(
            root=self.cfg.test.dataset.root,
            names_path=self.cfg.test.dataset.names_path,
            sf=self.cfg.test.dataset.sf,
            use_cache=self.cfg.test.dataset.use_cache
        )
        
        test_loader = SeedDataLoader(test_dataset, **self.cfg.test.loader)
        print(f"测试样本数量: {len(test_dataset)}")
        print(f"测试批次数量: {len(test_loader)}")
        return test_loader

def load_config(config_path):
    """加载配置文件"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        cfg_dict = yaml.safe_load(f)
    
    # 转换为config对象
    from torchlight.trainer.config import Config
    cfg = Config(cfg_dict)
    
    return cfg

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DaoGu数据集HSI-RefSR模型训练')
    parser.add_argument('mode', choices=['train', 'test', 'debug'], 
                       help='运行模式: train/test/debug')
    parser.add_argument('--config', '-c', default='daogu_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--save_dir', '-s', default='saved/daogu-training',
                       help='模型保存目录')
    parser.add_argument('--resume', '-r', default=None,
                       help='恢复训练的检查点')
    parser.add_argument('--resume_dir', default=None,
                       help='恢复训练的基础目录')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🚀 DaoGu数据集HSI-RefSR模型训练")
    print("=" * 80)
    print(f"运行模式: {args.mode}")
    print(f"配置文件: {args.config}")
    print(f"保存目录: {args.save_dir}")
    
    # 加载配置
    cfg = load_config(args.config)
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 设置随机种子
    setup_randomness(2021, deterministic=False)
    
    # 创建模型模块
    print("=== 创建模型模块 ===")
    model_module = get_obj(cfg.module, module)
    
    # 打印模型信息
    if hasattr(model_module, 'model'):
        total_params = sum(p.numel() for p in model_module.model.parameters())
        trainable_params = sum(p.numel() for p in model_module.model.parameters() if p.requires_grad)
        print(f"模型总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
        print(f"模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # 创建数据源
    data_source = DaoGuDataSource(cfg)
    
    # 运行训练/测试
    try:
        run_lazy(args, cfg._asdict(), model_module, data_source)
        print("✅ 运行完成！")
    except KeyboardInterrupt:
        print("⚠️ 运行被用户中断")
    except Exception as e:
        print(f"❌ 运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == '__main__':
    main()
