#!/usr/bin/env python3
"""
最小化训练脚本 - 只训练几个步骤验证可行性
"""

import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path

print("🚀 最小化训练开始...")
print("Step 1: 导入模块...")

from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

print("✅ 模块导入完成")
print("Step 2: 设置设备...")

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"✅ 使用设备: {device}")

print("Step 3: 加载配置...")
with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)
print("✅ 配置加载完成")

print("Step 4: 创建数据集...")
train_cfg = config['train']['dataset'].copy()
train_cfg.pop('type@', None)
train_dataset = DaoGuSRFDataset(**train_cfg)
print(f"✅ 数据集创建完成，样本数: {len(train_dataset)}")

print("Step 5: 创建数据加载器...")
train_loader = DataLoader(train_dataset, batch_size=1, shuffle=False, num_workers=0)
print("✅ 数据加载器创建完成")

print("Step 6: 创建模型...")
model = CrossNetHSI(
    use_mask=config['module']['model']['use_mask'],
    use_pwc=config['module']['model']['use_pwc'],
    reweight=config['module']['model']['reweight']
).to(device)
print(f"✅ 模型创建完成，参数数量: {sum(p.numel() for p in model.parameters()):,}")

print("Step 7: 创建优化器...")
optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-4)
criterion = nn.L1Loss()
print("✅ 优化器创建完成")

print("Step 8: 开始最小化训练...")
model.train()

# 只训练前3个批次
max_batches = 3
total_loss = 0.0

for batch_idx, batch in enumerate(train_loader):
    if batch_idx >= max_batches:
        break
        
    print(f"\n--- 批次 {batch_idx + 1}/{max_batches} ---")
    
    try:
        # 移动数据到GPU
        print("  移动数据到GPU...")
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
        hsi_hr = hsi_hr.to(device)
        hsi_lr = hsi_lr.to(device)
        hsi_rgb_lr = hsi_rgb_lr.to(device)
        rgb_hr = rgb_hr.to(device)
        
        print(f"  数据形状: HSI_LR={hsi_lr.shape}, RGB_HR={rgb_hr.shape}")
        
        # 前向传播
        print("  前向传播...")
        optimizer.zero_grad()
        start_time = time.time()
        output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
        forward_time = time.time() - start_time
        
        print(f"  前向传播完成 (用时: {forward_time:.2f}s), 输出形状: {output.shape}")
        
        # 计算损失
        print("  计算损失...")
        loss = criterion(output, hsi_hr)
        print(f"  损失值: {loss.item():.6f}")
        
        # 反向传播
        print("  反向传播...")
        start_time = time.time()
        loss.backward()
        backward_time = time.time() - start_time
        
        print(f"  反向传播完成 (用时: {backward_time:.2f}s)")
        
        # 优化器步骤
        print("  优化器更新...")
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        print(f"  ✅ 批次 {batch_idx + 1} 完成，损失: {loss.item():.6f}")
        
    except Exception as e:
        print(f"  ❌ 批次 {batch_idx + 1} 失败: {e}")
        import traceback
        traceback.print_exc()
        break

avg_loss = total_loss / max_batches if max_batches > 0 else 0
print(f"\n🎉 最小化训练完成！")
print(f"平均损失: {avg_loss:.6f}")

# 保存模型
save_dir = Path('saved/minimal-training')
save_dir.mkdir(parents=True, exist_ok=True)

torch.save({
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'avg_loss': avg_loss,
}, save_dir / 'minimal_model.pth')

print(f"✅ 模型保存到: {save_dir}")
print("\n🎯 最小化训练成功！证明DaoGu数据集可以用于HSI-RefSR模型训练")
