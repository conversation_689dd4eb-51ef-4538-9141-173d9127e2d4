#!/usr/bin/env python3
"""
4090训练准备检查脚本
确保所有组件都正确配置，可以在4090上运行
"""

import torch
import yaml
import numpy as np
from pathlib import Path
import sys

def check_gpu():
    """检查GPU配置"""
    print("🔍 检查GPU配置...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    print(f"✅ GPU: {gpu_name}")
    print(f"✅ GPU内存: {gpu_memory:.1f} GB")
    
    if "4090" in gpu_name or gpu_memory > 20:
        print("✅ GPU配置适合大尺寸训练")
        return True
    else:
        print(f"⚠️  GPU可能不是4090，但内存足够: {gpu_memory:.1f}GB")
        return gpu_memory > 8

def check_config():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_path = 'daogu_config.yaml'
    if not Path(config_path).exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 检查关键配置
    train_crop = config['train']['dataset']['crop_size']
    test_crop = config['test']['dataset']['crop_size']
    use_pwc = config['module']['model']['use_pwc']
    
    print(f"✅ 配置文件加载成功")
    print(f"  训练裁切尺寸: {train_crop}")
    print(f"  测试裁切尺寸: {test_crop}")
    print(f"  使用PWC对齐: {use_pwc}")
    
    if train_crop is None and test_crop is None:
        print("✅ 配置为原尺寸训练和测试")
        return True
    else:
        print("⚠️  配置仍使用裁切，建议改为null以使用原尺寸")
        return False

def check_data():
    """检查数据集"""
    print("\n🔍 检查数据集...")
    
    data_root = Path('data/daogu_processed')
    if not data_root.exists():
        print(f"❌ 数据目录不存在: {data_root}")
        return False
    
    # 检查必要的目录
    required_dirs = [
        'img1_hsi/HR',
        'img2_hsi/HR', 
        'img1/HR',
        'img2/HR'
    ]
    
    for dir_name in required_dirs:
        dir_path = data_root / dir_name
        if not dir_path.exists():
            print(f"❌ 缺少目录: {dir_path}")
            return False
        
        files = list(dir_path.glob('*'))
        print(f"✅ {dir_name}: {len(files)} 个文件")
    
    # 检查文件列表
    train_txt = data_root / 'train.txt'
    test_txt = data_root / 'test.txt'
    
    if not train_txt.exists():
        print(f"❌ 缺少训练文件列表: {train_txt}")
        return False
    
    if not test_txt.exists():
        print(f"❌ 缺少测试文件列表: {test_txt}")
        return False
    
    with open(train_txt, 'r', encoding='utf-8') as f:
        train_samples = [line.strip() for line in f if line.strip()]
    
    with open(test_txt, 'r', encoding='utf-8') as f:
        test_samples = [line.strip() for line in f if line.strip()]
    
    print(f"✅ 训练样本: {len(train_samples)} 个")
    print(f"✅ 测试样本: {len(test_samples)} 个")
    
    return True

def check_imports():
    """检查模块导入"""
    print("\n🔍 检查模块导入...")
    
    try:
        from daogu_dataset import DaoGuSRFDataset
        print("✅ DaoGuSRFDataset 导入成功")
    except Exception as e:
        print(f"❌ DaoGuSRFDataset 导入失败: {e}")
        return False
    
    try:
        from hsirsr.model.refsr import CrossNetHSI
        print("✅ CrossNetHSI 导入成功")
    except Exception as e:
        print(f"❌ CrossNetHSI 导入失败: {e}")
        return False
    
    return True

def test_data_loading():
    """测试数据加载"""
    print("\n🔍 测试数据加载...")
    
    try:
        from daogu_dataset import DaoGuSRFDataset
        
        # 创建数据集
        dataset = DaoGuSRFDataset(
            root='data/daogu_processed',
            input='img1',
            ref='img2',
            names_path='test.txt',
            sf=4,
            crop_size=None,  # 原尺寸
            use_cache=False
        )
        
        print(f"✅ 数据集创建成功: {len(dataset)} 个样本")
        
        # 测试加载第一个样本
        sample = dataset[0]
        print(f"✅ 样本加载成功")
        print(f"  数据形状: {[x.shape for x in sample]}")
        
        # 检查数据范围
        hsi_hr = sample[0]
        print(f"  HSI数据范围: [{hsi_hr.min():.3f}, {hsi_hr.max():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🔍 测试模型创建...")
    
    try:
        from hsirsr.model.refsr import CrossNetHSI
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 从配置文件读取模型参数
        with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        model_config = config['module']['model']
        model = CrossNetHSI(
            use_mask=model_config['use_mask'],
            use_pwc=model_config['use_pwc'],
            reweight=model_config['reweight']
        )
        model = model.to(device)
        
        print(f"✅ 模型创建成功")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  参数数量: {total_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_forward_pass():
    """测试前向传播"""
    print("\n🔍 测试前向传播...")
    
    try:
        from hsirsr.model.refsr import CrossNetHSI
        from daogu_dataset import DaoGuSRFDataset
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 从配置文件读取模型参数
        with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        model_config = config['module']['model']
        model = CrossNetHSI(
            use_mask=model_config['use_mask'],
            use_pwc=model_config['use_pwc'],
            reweight=model_config['reweight']
        )
        model = model.to(device)
        model.eval()
        
        # 创建数据集并加载样本
        dataset = DaoGuSRFDataset(
            root='data/daogu_processed',
            input='img1',
            ref='img2',
            names_path='test.txt',
            sf=4,
            crop_size=None,
            use_cache=False
        )
        
        sample = dataset[0]
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample
        
        # 转换为tensor
        hsi_lr_tensor = torch.from_numpy(hsi_lr).unsqueeze(0).to(device)
        hsi_rgb_lr_tensor = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).to(device)
        rgb_lr_tensor = torch.from_numpy(rgb_lr).unsqueeze(0).to(device)
        
        print(f"  输入形状:")
        print(f"    HSI LR: {hsi_lr_tensor.shape}")
        print(f"    HSI RGB LR: {hsi_rgb_lr_tensor.shape}")
        print(f"    RGB LR: {rgb_lr_tensor.shape}")
        
        # 前向传播
        with torch.no_grad():
            output = model(hsi_lr_tensor, hsi_rgb_lr_tensor, rgb_lr_tensor)
        
        print(f"✅ 前向传播成功")
        print(f"  输出形状: {output.shape}")
        
        # 检查GPU内存使用
        if torch.cuda.is_available():
            memory_used = torch.cuda.memory_allocated() / 1024**3
            print(f"  GPU内存使用: {memory_used:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主检查函数"""
    print("=" * 80)
    print("🚀 4090训练准备检查")
    print("=" * 80)
    
    checks = [
        ("GPU配置", check_gpu),
        ("配置文件", check_config),
        ("数据集", check_data),
        ("模块导入", check_imports),
        ("数据加载", test_data_loading),
        ("模型创建", test_model_creation),
        ("前向传播", test_forward_pass),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查出错: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 检查结果总结:")
    print("=" * 80)
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有检查通过！可以开始4090训练")
        print("运行命令: python train_daogu_4090.py")
    else:
        print("⚠️  部分检查失败，请修复后再开始训练")
    print("=" * 80)

if __name__ == '__main__':
    main()
