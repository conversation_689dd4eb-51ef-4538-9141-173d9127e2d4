"""
DaoGu数据集的自定义数据加载器
修改SRF以适配273个波段的SWIR数据
"""
import os
from pathlib import Path
import numpy as np
import torch.utils.data as data
from hdf5storage import loadmat
import imageio
from torchlight.transforms.functional import minmax_normalize
from torchlight.transforms.stateful import RandCrop
from hsirsr.data.transform import SRDegrade


def create_swir_srf(num_bands=273):
    """
    为SWIR数据创建简化的光谱响应函数
    由于我们的数据是996-2501nm，不在可见光范围内，
    我们使用简单的波段选择来模拟RGB
    """
    srf = np.zeros((3, num_bands), dtype=np.float32)
    
    # 对于SWIR数据，我们选择不同的波段来模拟RGB
    # R: 选择较长波长的波段
    # G: 选择中等波长的波段  
    # B: 选择较短波长的波段
    
    # 将273个波段分成3组
    band_per_channel = num_bands // 3
    
    # R通道：使用后1/3的波段
    r_start = 2 * band_per_channel
    r_end = num_bands
    srf[0, r_start:r_end] = 1.0 / (r_end - r_start)
    
    # G通道：使用中间1/3的波段
    g_start = band_per_channel
    g_end = 2 * band_per_channel
    srf[1, g_start:g_end] = 1.0 / (g_end - g_start)
    
    # B通道：使用前1/3的波段
    b_start = 0
    b_end = band_per_channel
    srf[2, b_start:b_end] = 1.0 / (b_end - b_start)
    
    return srf


class DaoGuSRFDataset(data.Dataset):
    """
    DaoGu数据集的SRF数据加载器
    适配273个波段的SWIR高光谱数据
    """

    def __init__(self, root, input, ref, names_path, sf, mat_key='gt', crop_size=None, repeat=1, use_cache=False):
        root = Path(root)
        self.hsi_inp_dir = root / (input+'_hsi') / 'HR'
        self.hsi_rgb_dir = root / input / 'HR'
        self.rgb_dir = root / ref / 'HR'
        
        self.sf = sf
        self.mat_key = mat_key
        self.repeat = repeat
        self.use_cache = use_cache
        
        # 读取样本名称
        with open(root / names_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        self.names = names * repeat
        
        # 创建适配SWIR数据的SRF
        self.srf = create_swir_srf(273)
        
        # 设置裁剪
        self.crop_size = crop_size
        self.crop = None  # 将在__getitem__中动态创建
            
        # 设置降采样
        self.degrade = SRDegrade(sf)
        
        print(f"DaoGu数据集初始化完成:")
        print(f"  样本数量: {len(self.names)}")
        print(f"  SRF形状: {self.srf.shape}")
        print(f"  缩放因子: {sf}")

    def get_mat(self, path):
        """加载.mat文件"""
        return loadmat(path)

    def get_png(self, path):
        """加载.png文件"""
        img = imageio.imread(path)
        img = np.array(img).astype('float')
        img = img / 255.0
        return img

    def __len__(self):
        return len(self.names)

    def __getitem__(self, index):
        name = self.names[index]
        
        # 加载HSI数据
        hsi_data = self.get_mat(str(self.hsi_inp_dir / (name+'.mat')))
        hsi_hr = hsi_data[self.mat_key]
        hsi_hr = minmax_normalize(hsi_hr.astype('float'))
        
        # 生成HSI低分辨率版本
        if 'lr' in hsi_data.keys():
            hsi_lr = hsi_data['lr'].transpose(1,2,0)
        else:
            hsi_lr = self.degrade(hsi_hr)

        # 使用SRF将HSI转换为RGB
        hsi_rgb_hr = hsi_hr @ self.srf.T
        hsi_rgb_lr = hsi_lr @ self.srf.T 

        # 加载RGB参考数据 (PNG格式)
        rgb_hsi_hr = self.get_png(str(self.hsi_rgb_dir / (name+'.png')))
        rgb_hsi_hr = minmax_normalize(rgb_hsi_hr.astype('float'))

        # 生成RGB低分辨率版本
        rgb_hsi_lr = self.degrade(rgb_hsi_hr)
        # RGB图像已经是RGB格式，不需要SRF转换
        rgb_hr = rgb_hsi_hr
        rgb_lr = rgb_hsi_lr

        # 应用裁剪
        if self.crop_size is not None:
            # 手动实现裁剪，因为RandCrop假设的数据格式不同
            h, w = hsi_hr.shape[:2]
            crop_h, crop_w = self.crop_size

            if h >= crop_h and w >= crop_w:
                # 随机选择裁剪位置
                import random
                start_h = random.randint(0, h - crop_h)
                start_w = random.randint(0, w - crop_w)

                # 裁剪HSI相关数据
                hsi_hr = hsi_hr[start_h:start_h+crop_h, start_w:start_w+crop_w]
                hsi_lr = hsi_lr[start_h:start_h+crop_h, start_w:start_w+crop_w]
                hsi_rgb_hr = hsi_rgb_hr[start_h:start_h+crop_h, start_w:start_w+crop_w]
                hsi_rgb_lr = hsi_rgb_lr[start_h:start_h+crop_h, start_w:start_w+crop_w]

                # RGB数据尺寸不同，需要按比例裁剪
                rgb_h, rgb_w = rgb_hsi_hr.shape[:2]
                scale_h = rgb_h / h
                scale_w = rgb_w / w

                rgb_start_h = int(start_h * scale_h)
                rgb_start_w = int(start_w * scale_w)
                rgb_crop_h = int(crop_h * scale_h)
                rgb_crop_w = int(crop_w * scale_w)

                # 确保不超出边界
                rgb_start_h = min(rgb_start_h, rgb_h - rgb_crop_h)
                rgb_start_w = min(rgb_start_w, rgb_w - rgb_crop_w)

                # 确保裁剪尺寸一致
                rgb_crop_h = min(rgb_crop_h, rgb_h - rgb_start_h)
                rgb_crop_w = min(rgb_crop_w, rgb_w - rgb_start_w)

                rgb_hr = rgb_hr[rgb_start_h:rgb_start_h+rgb_crop_h, rgb_start_w:rgb_start_w+rgb_crop_w]
                rgb_lr = rgb_lr[rgb_start_h:rgb_start_h+rgb_crop_h, rgb_start_w:rgb_start_w+rgb_crop_w]
                rgb_hsi_hr = rgb_hsi_hr[rgb_start_h:rgb_start_h+rgb_crop_h, rgb_start_w:rgb_start_w+rgb_crop_w]
            else:
                print(f"警告: 图像尺寸 {(h, w)} 小于裁剪尺寸 {self.crop_size}，跳过裁剪")

        # 转换为CHW格式
        def hwc2chw(img):
            return img.transpose(2, 0, 1)

        # 对于HSI数据，需要特殊处理以适配QRNN3D
        def hsi_hwc2chw(hsi):
            # HSI: (H, W, C) -> (1, C, H, W) 用于QRNN3D
            return hsi.transpose(2, 0, 1)[None, ...]  # 添加通道维度

        # 处理HSI数据（添加通道维度用于QRNN3D）
        hsi_hr_processed = hsi_hwc2chw(hsi_hr)
        hsi_lr_processed = hsi_hwc2chw(hsi_lr)

        # 处理RGB数据（正常的CHW转换）
        hsi_rgb_hr_processed = hwc2chw(hsi_rgb_hr)
        hsi_rgb_lr_processed = hwc2chw(hsi_rgb_lr)
        rgb_hr_processed = hwc2chw(rgb_hr)
        rgb_lr_processed = hwc2chw(rgb_lr)

        # 返回元组格式，与原始SRFDataset保持一致
        output = (hsi_hr_processed, hsi_lr_processed, hsi_rgb_hr_processed,
                 hsi_rgb_lr_processed, rgb_hr_processed, rgb_lr_processed)

        # 如果设置了裁剪尺寸，进行裁剪
        if self.crop_size:
            from torchlight.transforms.stateful import RandCrop
            H = output[0].shape[2]  # HSI数据的高度维度
            W = output[0].shape[3]  # HSI数据的宽度维度
            crop_fn = RandCrop((H, W), self.crop_size)
            output = tuple(crop_fn(o) for o in output)
        else:
            # 确保尺寸是32的倍数（FlowNet要求）
            def make_divisible_by_32(tensor):
                if len(tensor.shape) == 4:  # HSI数据 (1, C, H, W)
                    _, c, h, w = tensor.shape
                    new_h = (h // 32) * 32
                    new_w = (w // 32) * 32
                    if new_h > 0 and new_w > 0:
                        return tensor[:, :, :new_h, :new_w]
                elif len(tensor.shape) == 3:  # RGB数据 (C, H, W)
                    c, h, w = tensor.shape
                    new_h = (h // 32) * 32
                    new_w = (w // 32) * 32
                    if new_h > 0 and new_w > 0:
                        return tensor[:, :new_h, :new_w]
                return tensor

            output = tuple(make_divisible_by_32(o) for o in output)

        return output
