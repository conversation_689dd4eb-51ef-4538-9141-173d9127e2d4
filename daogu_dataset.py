"""
DaoGu数据集的自定义数据加载器
修改SRF以适配273个波段的SWIR数据
"""
import os
from pathlib import Path
import numpy as np
import torch.utils.data as data
from hdf5storage import loadmat
from torchlight.transforms.functional import minmax_normalize
from torchlight.transforms.stateful import RandCrop
from hsirsr.data.transform import SRDegrade


def create_swir_srf(num_bands=273):
    """
    为SWIR数据创建简化的光谱响应函数
    由于我们的数据是996-2501nm，不在可见光范围内，
    我们使用简单的波段选择来模拟RGB
    """
    srf = np.zeros((3, num_bands), dtype=np.float32)
    
    # 对于SWIR数据，我们选择不同的波段来模拟RGB
    # R: 选择较长波长的波段
    # G: 选择中等波长的波段  
    # B: 选择较短波长的波段
    
    # 将273个波段分成3组
    band_per_channel = num_bands // 3
    
    # R通道：使用后1/3的波段
    r_start = 2 * band_per_channel
    r_end = num_bands
    srf[0, r_start:r_end] = 1.0 / (r_end - r_start)
    
    # G通道：使用中间1/3的波段
    g_start = band_per_channel
    g_end = 2 * band_per_channel
    srf[1, g_start:g_end] = 1.0 / (g_end - g_start)
    
    # B通道：使用前1/3的波段
    b_start = 0
    b_end = band_per_channel
    srf[2, b_start:b_end] = 1.0 / (b_end - b_start)
    
    return srf


class DaoGuSRFDataset(data.Dataset):
    """
    DaoGu数据集的SRF数据加载器
    适配273个波段的SWIR高光谱数据
    """

    def __init__(self, root, input, ref, names_path, sf, mat_key='gt', crop_size=None, repeat=1, use_cache=False):
        root = Path(root)
        self.hsi_inp_dir = root / (input+'_hsi') / 'HR'
        self.hsi_rgb_dir = root / input / 'HR'
        self.rgb_dir = root / ref / 'HR'
        
        self.sf = sf
        self.mat_key = mat_key
        self.repeat = repeat
        self.use_cache = use_cache
        
        # 读取样本名称
        with open(root / names_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        self.names = names * repeat
        
        # 创建适配SWIR数据的SRF
        self.srf = create_swir_srf(273)
        
        # 设置裁剪
        if crop_size is not None:
            self.crop = RandCrop(crop_size)
        else:
            self.crop = None
            
        # 设置降采样
        self.degrade = SRDegrade(sf)
        
        print(f"DaoGu数据集初始化完成:")
        print(f"  样本数量: {len(self.names)}")
        print(f"  SRF形状: {self.srf.shape}")
        print(f"  缩放因子: {sf}")

    def get_mat(self, path):
        """加载.mat文件"""
        return loadmat(path)

    def __len__(self):
        return len(self.names)

    def __getitem__(self, index):
        name = self.names[index]
        
        # 加载HSI数据
        hsi_data = self.get_mat(str(self.hsi_inp_dir / (name+'.mat')))
        hsi_hr = hsi_data[self.mat_key]
        hsi_hr = minmax_normalize(hsi_hr.astype('float'))
        
        # 生成HSI低分辨率版本
        if 'lr' in hsi_data.keys():
            hsi_lr = hsi_data['lr'].transpose(1,2,0)
        else:
            hsi_lr = self.degrade(hsi_hr)

        # 使用SRF将HSI转换为RGB
        hsi_rgb_hr = hsi_hr @ self.srf.T
        hsi_rgb_lr = hsi_lr @ self.srf.T 

        # 加载RGB参考数据
        rgb_hsi_data = self.get_mat(str(self.hsi_rgb_dir / (name+'.mat')))
        rgb_hsi_hr = rgb_hsi_data[self.mat_key]
        rgb_hsi_hr = minmax_normalize(rgb_hsi_hr.astype('float'))

        # 生成RGB低分辨率版本
        rgb_hsi_lr = self.degrade(rgb_hsi_hr)
        rgb_hr = rgb_hsi_hr @ self.srf.T
        rgb_lr = rgb_hsi_lr @ self.srf.T

        # 应用裁剪
        if self.crop is not None:
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr, rgb_hsi_hr = \
                self.crop(hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr, rgb_hsi_hr)

        # 转换为CHW格式
        def hwc2chw(img):
            return img.transpose(2, 0, 1)

        return {
            'hsi_hr': hwc2chw(hsi_hr),
            'hsi_lr': hwc2chw(hsi_lr), 
            'hsi_rgb_hr': hwc2chw(hsi_rgb_hr),
            'hsi_rgb_lr': hwc2chw(hsi_rgb_lr),
            'rgb_hr': hwc2chw(rgb_hr),
            'rgb_lr': hwc2chw(rgb_lr),
            'rgb_hsi_hr': hwc2chw(rgb_hsi_hr),
            'name': name
        }
