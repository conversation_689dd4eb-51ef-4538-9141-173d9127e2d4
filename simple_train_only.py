#!/usr/bin/env python3
"""
仅训练脚本 - 不进行验证，避免尺寸不匹配问题
"""

import time
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from pathlib import Path
import numpy as np

print("🚀 开始仅训练模式...")

# 导入必要的模块
print("导入模块...")
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI
print("✅ 模块导入完成")

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载配置
print("加载配置...")
with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)
print("✅ 配置加载完成")

# 创建训练数据集
print("创建训练数据集...")
train_cfg = config['train']['dataset'].copy()
train_cfg.pop('type@', None)
train_dataset = DaoGuSRFDataset(**train_cfg)
print(f"✅ 训练数据集创建完成 - 样本数: {len(train_dataset)}")

# 创建数据加载器
print("创建数据加载器...")
train_loader = DataLoader(
    train_dataset,
    batch_size=1,
    shuffle=True,
    num_workers=0,
    pin_memory=True
)
print("✅ 数据加载器创建完成")

# 创建模型
print("创建模型...")
model = CrossNetHSI(
    use_mask=config['module']['model']['use_mask'],
    use_pwc=config['module']['model']['use_pwc'],
    reweight=config['module']['model']['reweight']
).to(device)

total_params = sum(p.numel() for p in model.parameters())
print(f"✅ 模型创建完成，参数数量: {total_params:,}")

# 创建优化器和损失函数
print("创建优化器...")
optimizer = optim.AdamW(
    model.parameters(),
    lr=config['module']['optimizer']['lr'],
    weight_decay=config['module']['optimizer']['weight_decay']
)

criterion = nn.L1Loss()
print("✅ 优化器创建完成")

# 创建保存目录
save_dir = Path('saved/daogu-train-only')
save_dir.mkdir(parents=True, exist_ok=True)
print(f"✅ 保存目录创建: {save_dir}")

# 开始训练
print("\n" + "="*80)
print("🚀 开始训练循环（仅训练模式）")
print("="*80)

max_epochs = 20  # 训练20个epoch
best_loss = float('inf')

for epoch in range(1, max_epochs + 1):
    print(f"\n=== Epoch {epoch}/{max_epochs} ===")
    start_time = time.time()
    
    # 训练
    model.train()
    total_loss = 0.0
    num_batches = len(train_loader)
    
    print(f"开始训练，共 {num_batches} 个批次...")
    
    for batch_idx, batch in enumerate(train_loader):
        try:
            # 移动数据到GPU
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = batch
            hsi_hr = hsi_hr.to(device)
            hsi_lr = hsi_lr.to(device)
            hsi_rgb_lr = hsi_rgb_lr.to(device)
            rgb_hr = rgb_hr.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            output, _, _, _ = model(hsi_sr=hsi_lr, hsi_rgb_sr=hsi_rgb_lr, ref_hr=rgb_hr)
            
            # 计算损失
            loss = criterion(output, hsi_hr)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            
            # 每5个批次打印一次
            if batch_idx % 5 == 0:
                print(f"  Batch {batch_idx+1}/{num_batches}: Loss={loss.item():.6f}")
                
        except Exception as e:
            print(f"  ⚠️ Batch {batch_idx+1} 跳过，错误: {e}")
            continue
    
    avg_loss = total_loss / num_batches
    epoch_time = time.time() - start_time
    
    print(f"Epoch {epoch} 完成 (用时: {epoch_time:.1f}s)")
    print(f"平均训练损失: {avg_loss:.6f}")
    
    # 保存模型
    if avg_loss < best_loss:
        best_loss = avg_loss
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': avg_loss,
            'best_loss': best_loss,
        }, save_dir / 'best_model.pth')
        print(f"✅ 保存最佳模型 (Loss: {best_loss:.6f})")
    
    # 每5个epoch保存一次检查点
    if epoch % 5 == 0:
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'train_loss': avg_loss,
        }, save_dir / f'checkpoint_epoch_{epoch}.pth')
        print(f"💾 保存检查点: epoch_{epoch}")
    
    print("-" * 80)

print(f"\n🎉 训练完成！最佳损失: {best_loss:.6f}")
print(f"模型保存在: {save_dir}")

# 保存最终模型
torch.save({
    'epoch': max_epochs,
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'train_loss': avg_loss,
    'best_loss': best_loss,
}, save_dir / 'final_model.pth')

print("✅ 最终模型已保存")
print("\n🎯 训练成功完成！DaoGu数据集HSI-RefSR模型训练完毕")
