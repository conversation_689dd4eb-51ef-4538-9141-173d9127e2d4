#!/usr/bin/env python3
"""
修正版原尺寸图像测试脚本 - 包含对齐模块测试
解决尺寸不匹配问题，正确测试HSI-RefSR模型的对齐功能
"""

import time
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import cv2
from scipy.io import loadmat

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模块
from hsirsr.model.refsr import CrossNetHSI

# 复制必要的函数
def minmax_normalize(img):
    """Min-max normalization"""
    return (img - img.min()) / (img.max() - img.min() + 1e-8)

def hwc2chw(img):
    """Convert HWC to CHW format"""
    return img.transpose(2, 0, 1)

def hsi_hwc2chw(img):
    """Convert HSI HWC to CHW format with additional dimension"""
    return img.transpose(2, 0, 1)[None, ...]  # [1, C, H, W]

class SRDegrade:
    """Simple degradation for super-resolution"""
    def __init__(self, scale_factor):
        self.scale_factor = scale_factor
    
    def __call__(self, img):
        h, w = img.shape[:2]
        new_h, new_w = h // self.scale_factor, w // self.scale_factor
        if len(img.shape) == 3:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
        else:
            return cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_CUBIC)

def create_swir_srf(num_bands):
    """创建适配SWIR数据的光谱响应函数"""
    srf = np.zeros((3, num_bands), dtype=np.float32)
    
    # 为SWIR波段创建简化的RGB映射
    band_per_channel = num_bands // 3
    
    # R通道 - 对应较长波长
    srf[0, :band_per_channel] = np.linspace(0.8, 0.2, band_per_channel)
    
    # G通道 - 对应中等波长  
    start_g = band_per_channel
    end_g = 2 * band_per_channel
    srf[1, start_g:end_g] = np.linspace(0.2, 0.8, band_per_channel)
    
    # B通道 - 对应较短波长
    start_b = 2 * band_per_channel
    srf[2, start_b:] = np.linspace(0.8, 0.2, num_bands - start_b)
    
    return srf

class FixedSizeDataset:
    """修正版数据集类 - 解决尺寸匹配问题"""
    
    def __init__(self, root, input, ref, names_path, sf):
        root = Path(root)
        self.hsi_inp_dir = root / (input+'_hsi') / 'HR'
        self.hsi_rgb_dir = root / input / 'HR'
        self.rgb_dir = root / ref / 'HR'
        
        self.sf = sf
        
        # 读取样本名称
        with open(root / names_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        self.names = names
        
        # 创建适配SWIR数据的SRF
        self.srf = create_swir_srf(273)
        
        # 设置降采样
        self.degrade = SRDegrade(sf)
        
        print(f"修正版数据集初始化完成:")
        print(f"  样本数量: {len(self.names)}")
        print(f"  SRF形状: {self.srf.shape}")
        print(f"  缩放因子: {sf}")

    def get_mat(self, path):
        """加载.mat文件"""
        return loadmat(path)

    def get_png(self, path):
        """加载.png文件"""
        import imageio
        img = imageio.imread(path)
        img = np.array(img).astype('float')
        img = img / 255.0
        return img

    def __len__(self):
        return len(self.names)

    def __getitem__(self, index):
        name = self.names[index]
        
        # 加载HSI数据 (MAT格式) - 原始尺寸
        hsi_hr = self.get_mat(str(self.hsi_inp_dir / (name+'.mat')))['gt']
        hsi_hr = minmax_normalize(hsi_hr.astype('float'))
        
        print(f"原始HSI尺寸: {hsi_hr.shape}")
        
        # 加载RGB参考数据 (PNG格式) - 偏移后的ground truth
        rgb_offset_hr = self.get_png(str(self.rgb_dir / (name+'.png')))
        rgb_offset_hr = minmax_normalize(rgb_offset_hr.astype('float'))
        
        # 加载HSI生成的伪RGB (PNG格式) - 下采样的
        hsi_pseudo_rgb_lr = self.get_png(str(self.hsi_rgb_dir / (name+'.png')))
        hsi_pseudo_rgb_lr = minmax_normalize(hsi_pseudo_rgb_lr.astype('float'))
        
        print(f"偏移RGB尺寸: {rgb_offset_hr.shape}")
        print(f"伪RGB LR尺寸: {hsi_pseudo_rgb_lr.shape}")
        
        # 使用SRF将HSI转换为RGB (生成高分辨率的伪RGB)
        hsi_rgb_hr = hsi_hr @ self.srf.T
        print(f"HSI生成RGB尺寸: {hsi_rgb_hr.shape}")
        
        # 确保HSI和RGB尺寸一致
        h, w = hsi_hr.shape[:2]
        
        # 调整RGB图像到HSI尺寸 (如果需要)
        if rgb_offset_hr.shape[:2] != (h, w):
            rgb_offset_hr = cv2.resize(rgb_offset_hr, (w, h), interpolation=cv2.INTER_LINEAR)
        if hsi_rgb_hr.shape[:2] != (h, w):
            hsi_rgb_hr = cv2.resize(hsi_rgb_hr, (w, h), interpolation=cv2.INTER_LINEAR)
        
        # 生成低分辨率版本
        hsi_lr = self.degrade(hsi_hr)
        hsi_rgb_lr = self.degrade(hsi_rgb_hr)  # 使用HSI生成的RGB的LR版本
        rgb_offset_lr = self.degrade(rgb_offset_hr)
        
        # 关键修正：确保模型输入的两个RGB图像尺寸相同
        # 方案1：将RGB HR下采样到与HSI RGB LR相同尺寸
        lr_h, lr_w = hsi_rgb_lr.shape[:2]
        rgb_offset_lr_matched = cv2.resize(rgb_offset_hr, (lr_w, lr_h), interpolation=cv2.INTER_LINEAR)
        
        print(f"修正后尺寸 - HSI_RGB_LR: {hsi_rgb_lr.shape}, RGB_OFFSET_LR: {rgb_offset_lr_matched.shape}")
        
        # 处理数据格式
        hsi_hr_processed = hsi_hwc2chw(hsi_hr).astype(np.float32)
        hsi_lr_processed = hsi_hwc2chw(hsi_lr).astype(np.float32)
        
        hsi_rgb_hr_processed = hwc2chw(hsi_rgb_hr).astype(np.float32)
        hsi_rgb_lr_processed = hwc2chw(hsi_rgb_lr).astype(np.float32)
        rgb_offset_hr_processed = hwc2chw(rgb_offset_hr).astype(np.float32)
        rgb_offset_lr_matched_processed = hwc2chw(rgb_offset_lr_matched).astype(np.float32)
        hsi_pseudo_rgb_lr_processed = hwc2chw(hsi_pseudo_rgb_lr).astype(np.float32)
        
        return {
            'name': name,
            'hsi_hr': hsi_hr_processed,                           # [1, 273, H, W] - 目标
            'hsi_lr': hsi_lr_processed,                           # [1, 273, h, w] - 输入
            'hsi_rgb_hr': hsi_rgb_hr_processed,                   # [3, H, W] - HSI生成的高分RGB
            'hsi_rgb_lr': hsi_rgb_lr_processed,                   # [3, h, w] - HSI生成的低分RGB (模型输入)
            'rgb_offset_hr': rgb_offset_hr_processed,             # [3, H, W] - 偏移后的ground truth (原始)
            'rgb_offset_lr_matched': rgb_offset_lr_matched_processed,  # [3, h, w] - 匹配尺寸的偏移RGB (模型输入)
            'hsi_pseudo_rgb_lr': hsi_pseudo_rgb_lr_processed,     # [3, h, w] - 原始伪RGB LR
            'original_shape': (h, w),
            'lr_shape': (lr_h, lr_w)
        }

def load_best_model():
    """加载最佳模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False).to(device)
    
    checkpoint = torch.load('saved/step-by-step-training/best_model.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, device

def calculate_alignment_error(img1, img2):
    """计算两张图像的对齐误差"""
    if isinstance(img1, torch.Tensor):
        img1 = img1.detach().cpu().numpy()
    if isinstance(img2, torch.Tensor):
        img2 = img2.detach().cpu().numpy()
    
    # 确保数据范围在[0,1]
    img1 = np.clip(img1, 0, 1)
    img2 = np.clip(img2, 0, 1)
    
    # 计算像素级差异
    diff = np.abs(img1 - img2)
    mean_error = np.mean(diff)
    max_error = np.max(diff)
    
    return diff, mean_error, max_error

def create_comprehensive_visualization(sample_data, model_output, save_dir, sample_idx):
    """创建全面的可视化分析"""
    
    name = sample_data['name']
    hsi_rgb_lr = sample_data['hsi_rgb_lr']                    # [3, h, w] HSI生成的低分RGB
    rgb_offset_hr = sample_data['rgb_offset_hr']              # [3, H, W] 偏移后的ground truth (原始)
    rgb_offset_lr_matched = sample_data['rgb_offset_lr_matched']  # [3, h, w] 匹配尺寸的偏移RGB
    hsi_pseudo_rgb_lr = sample_data['hsi_pseudo_rgb_lr']      # [3, h, w] 原始伪RGB LR
    
    # 模型输出
    hsi_sr_output = model_output['hsi_sr']                    # [273, H, W]
    ref_warp = model_output['ref_warp']                       # [3, h, w] 对齐后的RGB (LR尺寸)
    flow = model_output['flow']                               # [2, h, w] 光流 (LR尺寸)
    
    # 转换为numpy并调整维度
    hsi_rgb_lr_np = hsi_rgb_lr.cpu().numpy().transpose(1, 2, 0)                    # [h, w, 3]
    rgb_offset_hr_np = rgb_offset_hr.cpu().numpy().transpose(1, 2, 0)              # [H, W, 3]
    rgb_offset_lr_matched_np = rgb_offset_lr_matched.cpu().numpy().transpose(1, 2, 0)  # [h, w, 3]
    ref_warp_np = ref_warp.cpu().numpy().transpose(1, 2, 0)                        # [h, w, 3]
    hsi_pseudo_rgb_lr_np = hsi_pseudo_rgb_lr.cpu().numpy().transpose(1, 2, 0)      # [h, w, 3]
    
    # 计算对齐前后的差异 (在LR尺寸上)
    diff_before, mean_before, max_before = calculate_alignment_error(hsi_rgb_lr_np, rgb_offset_lr_matched_np)
    diff_after, mean_after, max_after = calculate_alignment_error(hsi_rgb_lr_np, ref_warp_np)
    
    # 创建可视化
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle(f'Sample {sample_idx+1} ({name}) - Complete Analysis with Alignment\n'
                 f'Original: {rgb_offset_hr_np.shape[0]}×{rgb_offset_hr_np.shape[1]} | '
                 f'Processing: {hsi_rgb_lr_np.shape[0]}×{hsi_rgb_lr_np.shape[1]} | '
                 f'Alignment Improvement: {((mean_before - mean_after) / mean_before * 100):.1f}%', 
                 fontsize=16, fontweight='bold')
    
    # 第一行：原始数据对比
    # 1. 偏移后的Ground Truth RGB (原始高分辨率)
    axes[0, 0].imshow(np.clip(rgb_offset_hr_np, 0, 1))
    axes[0, 0].set_title(f'Offset Ground Truth RGB\n{rgb_offset_hr_np.shape[0]}×{rgb_offset_hr_np.shape[1]} (Original HR)')
    axes[0, 0].axis('off')
    
    # 2. HSI生成的伪RGB (原始低分辨率)
    axes[0, 1].imshow(np.clip(hsi_pseudo_rgb_lr_np, 0, 1))
    axes[0, 1].set_title(f'HSI Pseudo RGB\n{hsi_pseudo_rgb_lr_np.shape[0]}×{hsi_pseudo_rgb_lr_np.shape[1]} (Original LR)')
    axes[0, 1].axis('off')
    
    # 3. HSI生成的RGB (处理用的低分辨率)
    axes[0, 2].imshow(np.clip(hsi_rgb_lr_np, 0, 1))
    axes[0, 2].set_title(f'HSI Generated RGB\n{hsi_rgb_lr_np.shape[0]}×{hsi_rgb_lr_np.shape[1]} (Processing LR)')
    axes[0, 2].axis('off')
    
    # 4. 匹配尺寸的偏移RGB
    axes[0, 3].imshow(np.clip(rgb_offset_lr_matched_np, 0, 1))
    axes[0, 3].set_title(f'Offset RGB (Matched Size)\n{rgb_offset_lr_matched_np.shape[0]}×{rgb_offset_lr_matched_np.shape[1]} (Before Alignment)')
    axes[0, 3].axis('off')
    
    # 第二行：对齐过程
    # 1. 对齐前差异
    im1 = axes[1, 0].imshow(diff_before, cmap='hot', vmin=0, vmax=0.3)
    axes[1, 0].set_title(f'Difference Before Alignment\nMean: {mean_before:.4f}, Max: {max_before:.4f}')
    axes[1, 0].axis('off')
    plt.colorbar(im1, ax=axes[1, 0], fraction=0.046)
    
    # 2. 对齐后的RGB
    axes[1, 1].imshow(np.clip(ref_warp_np, 0, 1))
    axes[1, 1].set_title(f'Aligned RGB\n{ref_warp_np.shape[0]}×{ref_warp_np.shape[1]} (After Alignment)')
    axes[1, 1].axis('off')
    
    # 3. 对齐后差异
    im2 = axes[1, 2].imshow(diff_after, cmap='hot', vmin=0, vmax=0.3)
    axes[1, 2].set_title(f'Difference After Alignment\nMean: {mean_after:.4f}, Max: {max_after:.4f}')
    axes[1, 2].axis('off')
    plt.colorbar(im2, ax=axes[1, 2], fraction=0.046)
    
    # 4. 光流可视化
    flow_np = flow.cpu().numpy()  # [2, h, w]
    flow_magnitude = np.sqrt(flow_np[0]**2 + flow_np[1]**2)
    im_flow = axes[1, 3].imshow(flow_magnitude, cmap='jet')
    axes[1, 3].set_title('Optical Flow Magnitude')
    axes[1, 3].axis('off')
    plt.colorbar(im_flow, ax=axes[1, 3], fraction=0.046)
    
    # 第三行：超分辨率结果
    # 1. HSI超分结果 (选择中间波段)
    mid_band = hsi_sr_output.shape[0] // 2
    hsi_sr_band = hsi_sr_output[mid_band].cpu().numpy()
    im3 = axes[2, 0].imshow(hsi_sr_band, cmap='viridis', vmin=0, vmax=1)
    axes[2, 0].set_title(f'HSI Super-Resolution\nBand {mid_band+1}/273')
    axes[2, 0].axis('off')
    plt.colorbar(im3, ax=axes[2, 0], fraction=0.046)
    
    # 2. HSI SR转RGB
    srf = create_swir_srf(273)
    hsi_sr_np = hsi_sr_output.cpu().numpy().transpose(1, 2, 0)  # [H, W, 273]
    hsi_sr_rgb = hsi_sr_np @ srf.T  # [H, W, 3]
    axes[2, 1].imshow(np.clip(hsi_sr_rgb, 0, 1))
    axes[2, 1].set_title(f'HSI SR → RGB\n{hsi_sr_rgb.shape[0]}×{hsi_sr_rgb.shape[1]} (Super-resolved)')
    axes[2, 1].axis('off')
    
    # 3. 最终对比：原始 vs 超分
    # 将原始RGB下采样到与超分结果相同尺寸进行对比
    rgb_hr_for_compare = cv2.resize(rgb_offset_hr_np, (hsi_sr_rgb.shape[1], hsi_sr_rgb.shape[0]), interpolation=cv2.INTER_LINEAR)
    final_diff = np.abs(rgb_hr_for_compare - hsi_sr_rgb)
    im4 = axes[2, 2].imshow(final_diff, cmap='hot', vmin=0, vmax=0.3)
    axes[2, 2].set_title('Final Difference\n(Ground Truth vs HSI SR)')
    axes[2, 2].axis('off')
    plt.colorbar(im4, ax=axes[2, 2], fraction=0.046)
    
    # 4. 统计信息
    axes[2, 3].axis('off')
    improvement_text = f"""
Alignment Results:
• Error Reduction: {((mean_before - mean_after) / mean_before * 100):.1f}%
• Before: {mean_before:.4f} ± {max_before:.4f}
• After: {mean_after:.4f} ± {max_after:.4f}

Flow Statistics:
• Mean Magnitude: {np.mean(flow_magnitude):.3f}
• Max Magnitude: {np.max(flow_magnitude):.3f}

Image Sizes:
• Original: {rgb_offset_hr_np.shape[0]}×{rgb_offset_hr_np.shape[1]}
• Processing: {hsi_rgb_lr_np.shape[0]}×{hsi_rgb_lr_np.shape[1]}
• Super-resolved: {hsi_sr_rgb.shape[0]}×{hsi_sr_rgb.shape[1]}

Final Quality:
• HSI Bands: 273
• Scale Factor: 4x
• Mean Final Diff: {np.mean(final_diff):.4f}
    """
    
    axes[2, 3].text(0.05, 0.95, improvement_text, transform=axes[2, 3].transAxes, 
                    fontsize=10, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(save_dir / f'sample_{sample_idx+1}_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return {
        'mean_error_before': mean_before,
        'mean_error_after': mean_after,
        'max_error_before': max_before,
        'max_error_after': max_after,
        'improvement_percent': (mean_before - mean_after) / mean_before * 100,
        'flow_magnitude_mean': np.mean(flow_magnitude),
        'flow_magnitude_max': np.max(flow_magnitude),
        'final_diff_mean': np.mean(final_diff)
    }

def test_fixed_size_with_alignment():
    """测试修正版原尺寸图像和对齐模块"""
    print("🔍 开始修正版原尺寸图像和对齐模块测试")
    print("=" * 80)
    
    # 加载模型
    model, device = load_best_model()
    print(f"✅ 模型加载完成，使用设备: {device}")
    
    # 创建修正版数据集
    dataset = FixedSizeDataset(
        root='data/daogu_processed',
        input='img1',
        ref='img2', 
        names_path='test.txt',
        sf=4
    )
    
    print(f"📊 测试数据集: {len(dataset)} 个样本")
    
    # 创建保存目录
    save_dir = Path('fixed_size_alignment_results')
    save_dir.mkdir(exist_ok=True)
    
    # 存储所有结果
    all_results = []
    criterion = nn.L1Loss()
    
    print("\n🚀 开始逐一测试所有样本...")
    
    with torch.no_grad():
        for sample_idx in range(min(3, len(dataset))):  # 先测试前3个样本
            print(f"\n--- 测试样本 {sample_idx+1}/{len(dataset)} ---")
            
            try:
                # 获取样本数据
                sample_data = dataset[sample_idx]
                name = sample_data['name']
                
                print(f"  样本名称: {name}")
                print(f"  原始尺寸: {sample_data['original_shape']}")
                print(f"  处理尺寸: {sample_data['lr_shape']}")
                
                # 移动数据到GPU
                hsi_hr = torch.from_numpy(sample_data['hsi_hr']).unsqueeze(0).to(device)
                hsi_lr = torch.from_numpy(sample_data['hsi_lr']).unsqueeze(0).to(device)
                hsi_rgb_lr = torch.from_numpy(sample_data['hsi_rgb_lr']).unsqueeze(0).to(device)
                rgb_offset_lr_matched = torch.from_numpy(sample_data['rgb_offset_lr_matched']).unsqueeze(0).to(device)
                
                print(f"  数据形状: HSI_LR={hsi_lr.shape}, HSI_RGB_LR={hsi_rgb_lr.shape}, RGB_LR={rgb_offset_lr_matched.shape}")
                
                # 前向传播 - 测试对齐模块
                start_time = time.time()
                hsi_sr_output, ref_warp, flow, masks = model(
                    hsi_sr=hsi_lr, 
                    hsi_rgb_sr=hsi_rgb_lr, 
                    ref_hr=rgb_offset_lr_matched  # 使用匹配尺寸的RGB
                )
                inference_time = time.time() - start_time
                
                # 计算损失
                loss = criterion(hsi_sr_output, hsi_hr)
                
                print(f"  ✅ 推理完成，Loss: {loss.item():.6f}")
                print(f"  ⏱️ 推理时间: {inference_time:.3f}s")
                print(f"  📐 输出尺寸: {hsi_sr_output.shape}")
                
                # 准备可视化数据
                model_output = {
                    'hsi_sr': hsi_sr_output[0],      # [1, 273, H, W] -> [273, H, W]
                    'ref_warp': ref_warp[0],         # [1, 3, h, w] -> [3, h, w]
                    'flow': flow[0],                 # [1, 2, h, w] -> [2, h, w]
                    'masks': masks[0] if masks is not None else None
                }
                
                # 生成全面的可视化分析
                print(f"  📊 生成全面分析...")
                alignment_stats = create_comprehensive_visualization(
                    sample_data, 
                    model_output, 
                    save_dir, 
                    sample_idx
                )
                
                # 保存结果
                result = {
                    'sample_idx': sample_idx,
                    'name': name,
                    'original_shape': sample_data['original_shape'],
                    'lr_shape': sample_data['lr_shape'],
                    'loss': loss.item(),
                    'inference_time': inference_time,
                    'alignment_stats': alignment_stats
                }
                
                all_results.append(result)
                
                print(f"  📈 对齐改善: {alignment_stats['improvement_percent']:.1f}%")
                print(f"  📊 最终差异: {alignment_stats['final_diff_mean']:.4f}")
                
                # 清理GPU缓存
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"  ❌ 样本 {sample_idx+1} 测试失败: {e}")
                import traceback
                traceback.print_exc()
                continue
    
    print(f"\n🎉 修正版对齐测试完成！")
    print(f"📁 结果保存在: {save_dir}")
    print(f"📊 共测试 {len(all_results)} 个样本")
    
    return all_results

def main():
    """主函数"""
    all_results = test_fixed_size_with_alignment()
    
    if all_results:
        alignment_improvements = [r['alignment_stats']['improvement_percent'] for r in all_results]
        final_diffs = [r['alignment_stats']['final_diff_mean'] for r in all_results]
        
        print(f"\n📈 修正版测试统计:")
        print(f"   平均对齐改善: {np.mean(alignment_improvements):.2f}% ± {np.std(alignment_improvements):.2f}%")
        print(f"   平均最终差异: {np.mean(final_diffs):.4f} ± {np.std(final_diffs):.4f}")
        print(f"   最佳对齐改善: {np.max(alignment_improvements):.2f}% (样本{np.argmax(alignment_improvements)+1})")

if __name__ == '__main__':
    main()
