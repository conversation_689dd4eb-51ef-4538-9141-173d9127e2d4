# DaoGu数据集HSI-RefSR训练配置 - 4090优化版
# 专为RTX 4090设计，支持原尺寸图像训练

train:
    dataset:
        type@: daogu_dataset.DaoGuSRFDataset
        input: img1
        ref: img2
        names_path: train.txt
        sf: 4
        crop_size: null  # 不裁切，使用原尺寸 (4090专用)
        repeat: 1
        use_cache: true
        root: data/daogu_processed
    loader:
        batch_size: 1  # 原尺寸图像，保持batch_size=1
        shuffle: true
        num_workers: 8  # 4090系统通常有更强的CPU
        pin_memory: true

test:
    dataset:
        type@: daogu_dataset.DaoGuSRFDataset
        input: img1
        ref: img2
        names_path: test.txt
        sf: 4
        crop_size: null  # 测试时也不裁切
        repeat: 1
        use_cache: true
        root: data/daogu_processed
    loader:
        batch_size: 1
        shuffle: false

engine:
    max_epochs: 200  # 4090可以训练更多轮次
    mnt_metric: val_psnr
    mnt_mode: max
    log_img_step: 5
    valid_log_img_step: 1
    pbar: qqdm
    save_per_epoch: 10  # 更频繁保存
    enable_tensorboard: true  # 启用tensorboard监控
    log_step: 5  # 更频繁的日志
    valid_per_epoch: 1
    enable_early_stop: true
    early_stop_threshold: 0.001  # 更严格的早停条件
    early_stop_count: 20
    num_fmt: '{:8.5g}'
    ckpt_save_mode: all

module:
    type@: refsr.CommonModule
    optimizer:
        type@: AdamW
        lr: 2.0e-05  # 4090可以使用更小的学习率进行精细训练
        weight_decay: 1.0e-04
        betas: [0.9, 0.999]
    scheduler:
        type@: CosineAnnealingLR
        T_max: 200
        eta_min: 1.0e-06
    model:
        type@: CrossNetHSI
        use_mask: true
        use_pwc: true  # 4090启用PWC对齐 (需要解决权限问题)
        reweight: false
