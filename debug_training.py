#!/usr/bin/env python3
"""
调试训练过程
"""

import torch
import yaml
from pathlib import Path
import sys
sys.path.append('.')

from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI

def debug_training():
    """调试训练过程"""
    print("🔍 调试训练过程")
    print("=" * 50)
    
    # 加载配置
    with open('daogu_config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print("✅ 配置加载完成")
    
    # 创建数据集
    try:
        dataset = DaoGuSRFDataset(
            root=config['train']['dataset']['root'],
            input=config['train']['dataset']['input'],
            ref=config['train']['dataset']['ref'],
            names_path=config['train']['dataset']['names_path'],
            sf=config['train']['dataset']['sf'],
            crop_size=config['train']['dataset']['crop_size'],
            repeat=1,  # 减少重复以便快速测试
            use_cache=config['train']['dataset']['use_cache']
        )
        print(f"✅ 数据集创建成功，样本数量: {len(dataset)}")
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        return False
    
    # 测试数据加载
    try:
        sample = dataset[0]
        print(f"✅ 数据加载成功")
        print(f"  数据形状: {[x.shape for x in sample]}")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 创建模型
    try:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = CrossNetHSI(use_mask=True, use_pwc=False, reweight=False)
        model = model.to(device)
        print(f"✅ 模型创建成功，设备: {device}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  模型参数数量: {total_params:,}")
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return False
    
    # 测试前向传播
    try:
        model.eval()
        with torch.no_grad():
            # 准备输入数据
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = sample
            
            # 转换为tensor并添加batch维度
            hsi_lr_tensor = torch.from_numpy(hsi_lr).unsqueeze(0).to(device)
            hsi_rgb_lr_tensor = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).to(device)
            rgb_lr_tensor = torch.from_numpy(rgb_lr).unsqueeze(0).to(device)
            
            print(f"  输入形状:")
            print(f"    HSI LR: {hsi_lr_tensor.shape}")
            print(f"    HSI RGB LR: {hsi_rgb_lr_tensor.shape}")
            print(f"    RGB LR: {rgb_lr_tensor.shape}")
            
            # 前向传播
            output = model(hsi_lr_tensor, hsi_rgb_lr_tensor, rgb_lr_tensor)
            print(f"✅ 前向传播成功")
            print(f"  输出形状: {output.shape}")
            
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试损失计算
    try:
        model.train()
        
        # 准备目标数据
        hsi_hr_tensor = torch.from_numpy(hsi_hr).unsqueeze(0).to(device)
        
        # 前向传播
        output = model(hsi_lr_tensor, hsi_rgb_lr_tensor, rgb_lr_tensor)
        
        # 计算损失
        criterion = torch.nn.L1Loss()
        loss = criterion(output, hsi_hr_tensor)
        
        print(f"✅ 损失计算成功")
        print(f"  损失值: {loss.item():.6f}")
        
    except Exception as e:
        print(f"❌ 损失计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试反向传播
    try:
        # 创建优化器
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=config['module']['optimizer']['lr'],
            weight_decay=config['module']['optimizer']['weight_decay']
        )
        
        # 清零梯度
        optimizer.zero_grad()
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        print(f"✅ 反向传播成功")
        print(f"  梯度范数: {grad_norm:.6f}")
        
        # 更新参数
        optimizer.step()
        print(f"✅ 参数更新成功")
        
    except Exception as e:
        print(f"❌ 反向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 所有测试通过！训练过程应该可以正常运行。")
    return True

if __name__ == '__main__':
    debug_training()
