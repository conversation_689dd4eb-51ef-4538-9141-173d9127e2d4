#!/usr/bin/env python3
"""
DaoGu数据集HSI-RefSR模型全面测试
生成详细的结果图表和性能分析
"""

import os
import numpy as np
import torch
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import seaborn as sns
from pathlib import Path
import time
from sklearn.metrics import mean_squared_error
from skimage.metrics import structural_similarity as ssim, peak_signal_noise_ratio
import cv2

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入模型和数据集
from daogu_dataset import DaoGuSRFDataset
from hsirsr.model.refsr import CrossNetHSI
import torch.utils.data as data

def setup_model():
    """初始化模型"""
    print("=== 初始化模型 ===")
    model = CrossNetHSI(reweight=False, use_mask=True, use_pwc=False)
    model.eval()
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")
    print(f"模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    return model

def setup_dataset():
    """初始化数据集"""
    print("\n=== 初始化数据集 ===")

    # 训练数据集
    train_dataset = DaoGuSRFDataset(
        root='data/daogu_processed',
        input='img1',
        ref='img2',
        names_path='train.txt',
        sf=4,
        crop_size=[128, 128]
    )

    # 测试数据集
    test_dataset = DaoGuSRFDataset(
        root='data/daogu_processed',
        input='img1',
        ref='img2',
        names_path='test.txt',
        sf=4,
        crop_size=[128, 128]
    )

    print(f"训练样本数量: {len(train_dataset)}")
    print(f"测试样本数量: {len(test_dataset)}")

    return train_dataset, test_dataset

def analyze_spectral_data(dataset, num_samples=5):
    """分析光谱数据特性"""
    print("\n=== 分析光谱数据特性 ===")
    
    # 收集光谱数据
    spectral_curves = []
    rgb_values = []
    
    for i in range(min(num_samples, len(dataset))):
        hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = dataset[i]
        
        # 提取中心像素的光谱曲线
        if i == 0:  # 只打印第一个样本的形状
            print(f"HSI HR shape: {hsi_hr.shape}")
            print(f"HSI RGB HR shape: {hsi_rgb_hr.shape}")

        # 转换为numpy数组（如果是tensor）
        if hasattr(hsi_hr, 'numpy'):
            hsi_hr_np = hsi_hr.numpy()
            hsi_rgb_hr_np = hsi_rgb_hr.numpy()
        else:
            hsi_hr_np = hsi_hr
            hsi_rgb_hr_np = hsi_rgb_hr

        if len(hsi_hr_np.shape) == 5:  # (1, 1, C, H, W)
            h, w = hsi_hr_np.shape[3], hsi_hr_np.shape[4]
            center_spectrum = hsi_hr_np[0, 0, :, h//2, w//2]
        elif len(hsi_hr_np.shape) == 4:  # (1, C, H, W)
            h, w = hsi_hr_np.shape[2], hsi_hr_np.shape[3]
            center_spectrum = hsi_hr_np[0, :, h//2, w//2]
        else:  # (C, H, W)
            h, w = hsi_hr_np.shape[1], hsi_hr_np.shape[2]
            center_spectrum = hsi_hr_np[:, h//2, w//2]

        spectral_curves.append(center_spectrum)

        # 提取RGB值
        if len(hsi_rgb_hr_np.shape) == 4:  # (1, C, H, W)
            rgb_center = hsi_rgb_hr_np[0, :, h//2, w//2]
        else:  # (C, H, W)
            rgb_center = hsi_rgb_hr_np[:, h//2, w//2]
        rgb_values.append(rgb_center)
    
    return np.array(spectral_curves), np.array(rgb_values)

def test_model_performance(model, dataset, num_samples=10):
    """测试模型性能"""
    print("\n=== 测试模型性能 ===")
    
    results = {
        'inference_times': [],
        'memory_usage': [],
        'psnr_values': [],
        'ssim_values': [],
        'mse_values': []
    }
    
    model.eval()
    with torch.no_grad():
        for i in range(min(num_samples, len(dataset))):
            print(f"测试样本 {i+1}/{min(num_samples, len(dataset))}")
            
            # 获取数据
            hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = dataset[i]
            
            # 转换为tensor并添加batch维度
            hsi_lr_batch = torch.from_numpy(hsi_lr).unsqueeze(0).float()
            hsi_rgb_lr_batch = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).float()
            rgb_hr_batch = torch.from_numpy(rgb_hr).unsqueeze(0).float()
            
            # 调整RGB尺寸以匹配HSI
            rgb_hr_resized = torch.nn.functional.interpolate(
                rgb_hr_batch, 
                size=(hsi_rgb_lr_batch.shape[2], hsi_rgb_lr_batch.shape[3]),
                mode='bilinear', 
                align_corners=False
            )
            
            # 测量推理时间
            start_time = time.time()
            
            try:
                # 模型推理
                output, ref_warp, flow, masks = model(hsi_lr_batch, hsi_rgb_lr_batch, rgb_hr_resized)
                
                inference_time = time.time() - start_time
                results['inference_times'].append(inference_time)
                
                # 计算指标（使用RGB图像进行评估）
                pred_rgb = output[0, 0, :, :, :].numpy()  # 取第一个通道作为示例
                # 转换hsi_hr为tensor如果需要
                if hasattr(hsi_hr, 'numpy'):
                    target_rgb = hsi_hr[0, 0, :, :, :].numpy()
                else:
                    # hsi_hr是numpy数组，形状是(1, 273, 128, 128)
                    target_rgb = hsi_hr[0, :, :, :]
                
                # 归一化到相同范围
                pred_rgb = (pred_rgb - pred_rgb.min()) / (pred_rgb.max() - pred_rgb.min())
                target_rgb = (target_rgb - target_rgb.min()) / (target_rgb.max() - target_rgb.min())
                
                # 计算PSNR (使用第一个光谱通道)
                psnr = peak_signal_noise_ratio(target_rgb[0], pred_rgb[0], data_range=1.0)
                results['psnr_values'].append(psnr)
                
                # 计算SSIM
                ssim_val = ssim(target_rgb[0], pred_rgb[0], data_range=1.0)
                results['ssim_values'].append(ssim_val)
                
                # 计算MSE
                mse = mean_squared_error(target_rgb.flatten(), pred_rgb.flatten())
                results['mse_values'].append(mse)
                
                print(f"  推理时间: {inference_time:.3f}s")
                print(f"  PSNR: {psnr:.2f}dB")
                print(f"  SSIM: {ssim_val:.4f}")
                print(f"  MSE: {mse:.6f}")
                
            except Exception as e:
                print(f"  模型推理失败: {e}")
                continue
    
    return results

def visualize_sample_results(model, dataset, sample_idx=0):
    """可视化单个样本的处理结果"""
    print(f"\n=== 可视化样本 {sample_idx} 的处理结果 ===")

    # 获取样本数据
    hsi_hr, hsi_lr, hsi_rgb_hr, hsi_rgb_lr, rgb_hr, rgb_lr = dataset[sample_idx]

    # 准备模型输入
    hsi_lr_batch = torch.from_numpy(hsi_lr).unsqueeze(0).float()
    hsi_rgb_lr_batch = torch.from_numpy(hsi_rgb_lr).unsqueeze(0).float()
    rgb_hr_batch = torch.from_numpy(rgb_hr).unsqueeze(0).float()

    # 调整RGB尺寸
    rgb_hr_resized = torch.nn.functional.interpolate(
        rgb_hr_batch,
        size=(hsi_rgb_lr_batch.shape[2], hsi_rgb_lr_batch.shape[3]),
        mode='bilinear',
        align_corners=False
    )

    # 模型推理
    model.eval()
    with torch.no_grad():
        output, ref_warp, flow, masks = model(hsi_lr_batch, hsi_rgb_lr_batch, rgb_hr_resized)

    # 创建可视化图
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle(f'样本 {sample_idx} 处理结果展示', fontsize=16, fontweight='bold')

    # 第一行：输入数据
    # HSI伪彩色图像（选择3个波段作为RGB）
    # hsi_hr形状是(1, 273, 128, 128)
    if hasattr(hsi_hr, 'numpy'):
        hsi_hr_np = hsi_hr.numpy()
    else:
        hsi_hr_np = hsi_hr

    hsi_pseudo_rgb = hsi_hr_np[0, [50, 100, 150], :, :].transpose(1, 2, 0)
    hsi_pseudo_rgb = (hsi_pseudo_rgb - hsi_pseudo_rgb.min()) / (hsi_pseudo_rgb.max() - hsi_pseudo_rgb.min())
    axes[0, 0].imshow(hsi_pseudo_rgb)
    axes[0, 0].set_title('HSI伪彩色图像\n(波段50,100,150)')
    axes[0, 0].axis('off')

    # HSI生成的RGB图像
    if hasattr(hsi_rgb_hr, 'numpy'):
        hsi_rgb_img = hsi_rgb_hr.numpy().transpose(1, 2, 0)
    else:
        hsi_rgb_img = hsi_rgb_hr.transpose(1, 2, 0)
    hsi_rgb_img = np.clip(hsi_rgb_img, 0, 1)
    axes[0, 1].imshow(hsi_rgb_img)
    axes[0, 1].set_title('HSI生成的RGB图像')
    axes[0, 1].axis('off')

    # 偏移的RGB图像
    if hasattr(rgb_hr, 'numpy'):
        rgb_img = rgb_hr.numpy().transpose(1, 2, 0)
    else:
        rgb_img = rgb_hr.transpose(1, 2, 0)
    rgb_img = np.clip(rgb_img, 0, 1)
    axes[0, 2].imshow(rgb_img)
    axes[0, 2].set_title('偏移的RGB图像')
    axes[0, 2].axis('off')

    # 光流可视化
    flow_vis = flow[0].numpy().transpose(1, 2, 0)
    flow_magnitude = np.sqrt(flow_vis[:, :, 0]**2 + flow_vis[:, :, 1]**2)
    im = axes[0, 3].imshow(flow_magnitude, cmap='jet')
    axes[0, 3].set_title('光流幅度')
    axes[0, 3].axis('off')
    plt.colorbar(im, ax=axes[0, 3], fraction=0.046, pad=0.04)

    # 第二行：处理结果
    # 模型输出（选择3个波段）
    output_pseudo_rgb = output[0, 0, [50, 100, 150], :, :].numpy().transpose(1, 2, 0)
    output_pseudo_rgb = (output_pseudo_rgb - output_pseudo_rgb.min()) / (output_pseudo_rgb.max() - output_pseudo_rgb.min())
    axes[1, 0].imshow(output_pseudo_rgb)
    axes[1, 0].set_title('模型输出\n(波段50,100,150)')
    axes[1, 0].axis('off')

    # 对齐后的参考图像
    ref_warp_img = ref_warp[0].numpy().transpose(1, 2, 0)
    ref_warp_img = np.clip(ref_warp_img, 0, 1)
    axes[1, 1].imshow(ref_warp_img)
    axes[1, 1].set_title('对齐后的参考图像')
    axes[1, 1].axis('off')

    # 差异图
    hsi_hr_tensor = torch.from_numpy(hsi_hr_np).unsqueeze(0)
    if output.shape == hsi_hr_tensor.shape:
        diff = torch.abs(output[0, 0, 100, :, :] - hsi_hr_tensor[0, 0, 100, :, :]).numpy()
        im = axes[1, 2].imshow(diff, cmap='hot')
        axes[1, 2].set_title('差异图 (波段100)')
        axes[1, 2].axis('off')
        plt.colorbar(im, ax=axes[1, 2], fraction=0.046, pad=0.04)
    else:
        axes[1, 2].text(0.5, 0.5, f'尺寸不匹配\n输出:{output.shape}\n目标:{hsi_hr_tensor.shape}',
                       ha='center', va='center', transform=axes[1, 2].transAxes)
        axes[1, 2].set_title('差异图')
        axes[1, 2].axis('off')

    # 掩码可视化
    if masks and len(masks) > 0:
        mask_vis = masks[0][0, 0].numpy()
        im = axes[1, 3].imshow(mask_vis, cmap='gray')
        axes[1, 3].set_title('注意力掩码')
        axes[1, 3].axis('off')
        plt.colorbar(im, ax=axes[1, 3], fraction=0.046, pad=0.04)
    else:
        axes[1, 3].text(0.5, 0.5, '无掩码数据', ha='center', va='center', transform=axes[1, 3].transAxes)
        axes[1, 3].set_title('注意力掩码')
        axes[1, 3].axis('off')

    # 第三行：光谱分析
    # 中心像素光谱曲线对比
    center_h, center_w = hsi_hr_np.shape[2] // 2, hsi_hr_np.shape[3] // 2
    wavelengths = np.linspace(996, 2501, 273)

    original_spectrum = hsi_hr_np[0, :, center_h, center_w]
    output_spectrum = output[0, 0, :, center_h, center_w].numpy()

    axes[2, 0].plot(wavelengths, original_spectrum, 'b-', label='原始', linewidth=2)
    axes[2, 0].plot(wavelengths, output_spectrum, 'r--', label='重建', linewidth=2)
    axes[2, 0].set_xlabel('波长 (nm)')
    axes[2, 0].set_ylabel('反射率')
    axes[2, 0].set_title('中心像素光谱对比')
    axes[2, 0].legend()
    axes[2, 0].grid(True, alpha=0.3)

    # 光谱角度误差
    spectral_angles = []
    for i in range(0, hsi_hr_np.shape[2], 4):  # 每4个像素采样一次
        for j in range(0, hsi_hr_np.shape[3], 4):
            orig_spec = hsi_hr_np[0, :, i, j]
            out_spec = output[0, 0, :, i, j].numpy()

            # 计算光谱角度
            cos_angle = np.dot(orig_spec, out_spec) / (np.linalg.norm(orig_spec) * np.linalg.norm(out_spec))
            angle = np.arccos(np.clip(cos_angle, -1, 1)) * 180 / np.pi
            spectral_angles.append(angle)

    axes[2, 1].hist(spectral_angles, bins=20, alpha=0.7, color='purple', edgecolor='black')
    axes[2, 1].set_xlabel('光谱角度误差 (度)')
    axes[2, 1].set_ylabel('频次')
    axes[2, 1].set_title('光谱角度误差分布')
    axes[2, 1].axvline(np.mean(spectral_angles), color='red', linestyle='--',
                      label=f'均值: {np.mean(spectral_angles):.2f}°')
    axes[2, 1].legend()

    # RGB通道对比
    rgb_channels = ['R', 'G', 'B']
    if hasattr(hsi_rgb_hr, 'numpy'):
        original_rgb = hsi_rgb_hr[:, center_h, center_w].numpy()
    else:
        original_rgb = hsi_rgb_hr[:, center_h, center_w]

    x = np.arange(len(rgb_channels))
    width = 0.35

    axes[2, 2].bar(x - width/2, original_rgb, width, label='原始RGB', alpha=0.7)
    axes[2, 2].bar(x + width/2, ref_warp_img[center_h, center_w], width, label='对齐RGB', alpha=0.7)
    axes[2, 2].set_xlabel('RGB通道')
    axes[2, 2].set_ylabel('像素值')
    axes[2, 2].set_title('中心像素RGB值对比')
    axes[2, 2].set_xticks(x)
    axes[2, 2].set_xticklabels(rgb_channels)
    axes[2, 2].legend()

    # 统计信息
    stats_text = f"""统计信息:
光谱角度误差均值: {np.mean(spectral_angles):.2f}°
光谱角度误差标准差: {np.std(spectral_angles):.2f}°
输出数据范围: [{output.min():.4f}, {output.max():.4f}]
原始数据范围: [{hsi_hr_np.min():.4f}, {hsi_hr_np.max():.4f}]
光流范围: [{flow.min():.4f}, {flow.max():.4f}]"""

    axes[2, 3].text(0.05, 0.95, stats_text, transform=axes[2, 3].transAxes,
                    verticalalignment='top', fontfamily='monospace', fontsize=10)
    axes[2, 3].set_title('处理统计信息')
    axes[2, 3].axis('off')

    plt.tight_layout()
    return fig

def create_comprehensive_visualization(spectral_data, rgb_data, performance_results, dataset):
    """创建全面的可视化结果"""
    print("\n=== 生成可视化结果 ===")

    # 创建大图
    fig = plt.figure(figsize=(20, 16))
    gs = GridSpec(4, 4, figure=fig, hspace=0.3, wspace=0.3)
    
    # 1. 光谱曲线分析
    ax1 = fig.add_subplot(gs[0, :2])
    wavelengths = np.linspace(996, 2501, 273)  # SWIR波长范围
    for i, spectrum in enumerate(spectral_data):
        ax1.plot(wavelengths, spectrum, alpha=0.7, label=f'样本 {i+1}')
    ax1.set_xlabel('波长 (nm)')
    ax1.set_ylabel('反射率')
    ax1.set_title('SWIR光谱曲线分析 (273个波段)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. RGB值分布
    ax2 = fig.add_subplot(gs[0, 2:])
    rgb_mean = np.mean(rgb_data, axis=0)
    rgb_std = np.std(rgb_data, axis=0)
    colors = ['red', 'green', 'blue']
    channels = ['R', 'G', 'B']
    
    x = np.arange(len(channels))
    bars = ax2.bar(x, rgb_mean, yerr=rgb_std, capsize=5, color=colors, alpha=0.7)
    ax2.set_xlabel('RGB通道')
    ax2.set_ylabel('平均值')
    ax2.set_title('伪RGB图像通道分布')
    ax2.set_xticks(x)
    ax2.set_xticklabels(channels)
    
    # 添加数值标签
    for i, (bar, mean_val, std_val) in enumerate(zip(bars, rgb_mean, rgb_std)):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std_val + 0.01,
                f'{mean_val:.3f}±{std_val:.3f}', ha='center', va='bottom')
    
    # 3. 性能指标分布
    if performance_results['psnr_values']:
        ax3 = fig.add_subplot(gs[1, 0])
        ax3.hist(performance_results['psnr_values'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.set_xlabel('PSNR (dB)')
        ax3.set_ylabel('频次')
        ax3.set_title('PSNR分布')
        ax3.axvline(np.mean(performance_results['psnr_values']), color='red', linestyle='--', 
                   label=f'均值: {np.mean(performance_results["psnr_values"]):.2f}')
        ax3.legend()
        
        ax4 = fig.add_subplot(gs[1, 1])
        ax4.hist(performance_results['ssim_values'], bins=10, alpha=0.7, color='lightgreen', edgecolor='black')
        ax4.set_xlabel('SSIM')
        ax4.set_ylabel('频次')
        ax4.set_title('SSIM分布')
        ax4.axvline(np.mean(performance_results['ssim_values']), color='red', linestyle='--',
                   label=f'均值: {np.mean(performance_results["ssim_values"]):.4f}')
        ax4.legend()
        
        ax5 = fig.add_subplot(gs[1, 2])
        ax5.hist(performance_results['inference_times'], bins=10, alpha=0.7, color='orange', edgecolor='black')
        ax5.set_xlabel('推理时间 (s)')
        ax5.set_ylabel('频次')
        ax5.set_title('推理时间分布')
        ax5.axvline(np.mean(performance_results['inference_times']), color='red', linestyle='--',
                   label=f'均值: {np.mean(performance_results["inference_times"]):.3f}')
        ax5.legend()
    
    # 4. 数据集统计
    ax6 = fig.add_subplot(gs[1, 3])
    dataset_stats = {
        '训练样本': len(dataset),
        '光谱通道': 273,
        '空间尺寸': '128×128',
        '缩放因子': 4
    }
    
    y_pos = np.arange(len(dataset_stats))
    values = [len(dataset), 273, 128, 4]
    bars = ax6.barh(y_pos, values, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    ax6.set_yticks(y_pos)
    ax6.set_yticklabels(list(dataset_stats.keys()))
    ax6.set_xlabel('数值')
    ax6.set_title('数据集统计信息')
    
    # 添加数值标签
    for i, (bar, val) in enumerate(zip(bars, values)):
        ax6.text(bar.get_width() + max(values)*0.01, bar.get_y() + bar.get_height()/2,
                str(val), ha='left', va='center')
    
    return fig

def create_model_architecture_summary(model):
    """创建模型架构总结图"""
    print("\n=== 生成模型架构总结 ===")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('HSI-RefSR模型架构分析', fontsize=16, fontweight='bold')

    # 1. 模型组件分析
    components = {
        'FlowNet1': sum(p.numel() for p in model.flownet1.parameters()),
        'FlowNet2': sum(p.numel() for p in model.flownet2.parameters()),
        'HSI编码器': sum(p.numel() for p in model.hsi_encoder.parameters()),
        'RGB编码器': sum(p.numel() for p in model.rgb_encoder.parameters()),
        '解码器': sum(p.numel() for p in model.decoder.parameters()),
    }

    if hasattr(model, 'mask_predictor') and model.mask_predictor:
        components['掩码预测器'] = sum(p.numel() for p in model.mask_predictor.parameters())

    # 饼图显示参数分布
    sizes = list(components.values())
    labels = list(components.keys())
    colors = plt.cm.Set3(np.linspace(0, 1, len(components)))

    wedges, texts, autotexts = axes[0, 0].pie(sizes, labels=labels, autopct='%1.1f%%',
                                             colors=colors, startangle=90)
    axes[0, 0].set_title('模型参数分布')

    # 2. 参数数量柱状图
    y_pos = np.arange(len(components))
    bars = axes[0, 1].barh(y_pos, [v/1e6 for v in sizes], color=colors)
    axes[0, 1].set_yticks(y_pos)
    axes[0, 1].set_yticklabels(labels)
    axes[0, 1].set_xlabel('参数数量 (百万)')
    axes[0, 1].set_title('各组件参数数量')

    # 添加数值标签
    for i, (bar, val) in enumerate(zip(bars, sizes)):
        axes[0, 1].text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                        f'{val/1e6:.2f}M', ha='left', va='center')

    # 3. 数据流图
    axes[1, 0].text(0.5, 0.9, 'HSI-RefSR数据流', ha='center', va='top',
                   transform=axes[1, 0].transAxes, fontsize=14, fontweight='bold')

    flow_text = """
输入:
├── HSI低分辨率 (1×273×H×W)
├── HSI伪RGB (3×H×W)
└── 偏移RGB (3×H×W)

处理流程:
1. FlowNet → 光流估计
2. 图像对齐 → 对齐RGB
3. HSI编码器 → HSI特征
4. RGB编码器 → RGB特征
5. 特征融合 → 解码器
6. 输出HSI高分辨率 (1×273×H×W)
"""

    axes[1, 0].text(0.05, 0.8, flow_text, transform=axes[1, 0].transAxes,
                   verticalalignment='top', fontfamily='monospace', fontsize=10)
    axes[1, 0].axis('off')

    # 4. 技术特点总结
    tech_features = {
        'QRNN3D层': '处理3D高光谱数据',
        '双向QRNN': '前向后向信息融合',
        'FlowNet': '光流估计与图像对齐',
        '注意力机制': '自适应特征权重',
        '多尺度处理': '不同分辨率特征',
        '残差连接': '梯度流优化'
    }

    y_positions = np.linspace(0.9, 0.1, len(tech_features))
    for i, (feature, description) in enumerate(tech_features.items()):
        axes[1, 1].text(0.05, y_positions[i], f"• {feature}: {description}",
                       transform=axes[1, 1].transAxes, fontsize=11)

    axes[1, 1].set_title('关键技术特点')
    axes[1, 1].axis('off')

    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("开始DaoGu数据集HSI-RefSR模型全面测试")
    print("=" * 60)
    
    # 创建结果目录
    results_dir = Path("test_results")
    results_dir.mkdir(exist_ok=True)
    
    try:
        # 1. 初始化
        model = setup_model()
        train_dataset, test_dataset = setup_dataset()
        
        # 2. 分析光谱数据
        spectral_data, rgb_data = analyze_spectral_data(train_dataset, num_samples=10)
        
        # 3. 测试模型性能
        performance_results = test_model_performance(model, test_dataset, num_samples=5)
        
        # 4. 生成可视化结果
        fig1 = create_comprehensive_visualization(spectral_data, rgb_data, performance_results, train_dataset)

        # 5. 生成样本处理结果可视化
        fig2 = visualize_sample_results(model, test_dataset, sample_idx=0)

        # 6. 生成模型架构总结
        fig3 = create_model_architecture_summary(model)

        # 7. 保存结果
        output_path1 = results_dir / "comprehensive_test_results.png"
        fig1.savefig(output_path1, dpi=300, bbox_inches='tight')
        print(f"\n综合测试结果图已保存到: {output_path1}")

        output_path2 = results_dir / "sample_processing_results.png"
        fig2.savefig(output_path2, dpi=300, bbox_inches='tight')
        print(f"样本处理结果图已保存到: {output_path2}")

        output_path3 = results_dir / "model_architecture_summary.png"
        fig3.savefig(output_path3, dpi=300, bbox_inches='tight')
        print(f"模型架构总结图已保存到: {output_path3}")
        
        # 6. 打印总结报告
        print("\n" + "=" * 60)
        print("测试总结报告")
        print("=" * 60)
        
        if performance_results['psnr_values']:
            print(f"平均PSNR: {np.mean(performance_results['psnr_values']):.2f} ± {np.std(performance_results['psnr_values']):.2f} dB")
            print(f"平均SSIM: {np.mean(performance_results['ssim_values']):.4f} ± {np.std(performance_results['ssim_values']):.4f}")
            print(f"平均推理时间: {np.mean(performance_results['inference_times']):.3f} ± {np.std(performance_results['inference_times']):.3f} s")
            print(f"平均MSE: {np.mean(performance_results['mse_values']):.6f} ± {np.std(performance_results['mse_values']):.6f}")
        
        print(f"光谱数据维度: {spectral_data.shape}")
        print(f"RGB数据维度: {rgb_data.shape}")
        print(f"训练样本数量: {len(train_dataset)}")
        print(f"测试样本数量: {len(test_dataset)}")
        
        print("\n✅ 全面测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
